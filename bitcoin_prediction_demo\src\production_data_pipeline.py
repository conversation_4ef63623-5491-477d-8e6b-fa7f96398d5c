#!/usr/bin/env python3
"""
Production Data Pipeline for Bitcoin Price Prediction
====================================================

This module provides a production-ready data pipeline that integrates
with existing feature engineering and data processing capabilities.

Author: Bitcoin Price Prediction Production System
Date: 2025-07-07
"""

import pandas as pd
import numpy as np
import yfinance as yf
import warnings
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import sys
import os

# Add parent directory to path to import existing modules
sys.path.append(str(Path(__file__).parent.parent.parent))

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionDataPipeline:
    """
    Production-ready data pipeline that integrates with existing
    feature engineering and provides real-time data processing.
    """
    
    def __init__(self, config_path: str = "../config.yaml"):
        """Initialize the production data pipeline."""
        import yaml
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.symbol = self.config['data']['symbol']
        self.existing_data_path = self.config['data']['paths']['existing_processed']
        self.feature_columns = None
        
        # Load existing processed data for reference
        self._load_existing_data_info()
        
        logger.info("Production Data Pipeline initialized successfully")
    
    def _load_existing_data_info(self):
        """Load information about existing processed data."""
        try:
            if Path(self.existing_data_path).exists():
                # Load just the header to get column information
                sample_data = pd.read_csv(self.existing_data_path, nrows=1, index_col=0)
                self.feature_columns = list(sample_data.columns)
                logger.info(f"✓ Existing data structure loaded: {len(self.feature_columns)} columns")
            else:
                logger.warning("Existing processed data not found")
                
        except Exception as e:
            logger.error(f"Error loading existing data info: {str(e)}")
    
    def get_latest_data(self, period: str = "1y") -> pd.DataFrame:
        """
        Fetch latest Bitcoin data from Yahoo Finance.
        
        Args:
            period: Data period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
        
        Returns:
            DataFrame with latest Bitcoin price data
        """
        try:
            logger.info(f"Fetching latest {self.symbol} data for period: {period}")
            
            # Fetch data from Yahoo Finance
            ticker = yf.Ticker(self.symbol)
            data = ticker.history(period=period)
            
            if data.empty:
                logger.error("No data retrieved from Yahoo Finance")
                return pd.DataFrame()
            
            # Standardize column names to match existing data
            data.columns = [col.lower().replace(' ', '_') for col in data.columns]
            data.index.name = 'Date'
            
            # Add adj_close if not present (same as close for crypto)
            if 'adj_close' not in data.columns:
                data['adj_close'] = data['close']
            
            logger.info(f"✓ Retrieved {len(data)} rows of data")
            return data
            
        except Exception as e:
            logger.error(f"Error fetching latest data: {str(e)}")
            return pd.DataFrame()
    
    def calculate_basic_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate basic features that match the existing data structure.
        
        Args:
            data: Raw price data
        
        Returns:
            DataFrame with basic features added
        """
        try:
            df = data.copy()
            
            # Basic return calculations
            df['daily_return'] = df['close'].pct_change()
            df['log_return'] = np.log(df['close'] / df['close'].shift(1))
            
            # Volatility measures
            df['volatility_7d'] = df['daily_return'].rolling(window=7).std()
            df['volatility_30d'] = df['daily_return'].rolling(window=30).std()
            df['volatility_90d'] = df['daily_return'].rolling(window=90).std()
            
            # Time-based features
            df['year'] = df.index.year
            df['month'] = df.index.month
            df['day_of_week'] = df.index.dayofweek
            df['day_of_year'] = df.index.dayofyear
            
            logger.info("✓ Basic features calculated")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating basic features: {str(e)}")
            return data
    
    def apply_existing_feature_engineering(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Apply the existing feature engineering pipeline to new data.
        
        Args:
            data: DataFrame with basic features
        
        Returns:
            DataFrame with full feature set matching existing structure
        """
        try:
            # Import existing feature engineering functions
            from feature_engineering import (
                create_lag_features,
                create_rolling_features, 
                create_technical_indicators,
                create_volatility_features,
                create_time_features,
                create_target_variables
            )
            
            df = data.copy()
            
            # Apply feature engineering steps
            logger.info("Applying existing feature engineering pipeline...")
            
            # Lag features
            df = create_lag_features(df)
            
            # Rolling features
            df = create_rolling_features(df)
            
            # Technical indicators
            df = create_technical_indicators(df)
            
            # Volatility features
            df = create_volatility_features(df)
            
            # Time-based features
            df = create_time_features(df)
            
            # Target variables (for training data)
            df = create_target_variables(df)
            
            logger.info("✓ Feature engineering pipeline applied successfully")
            return df
            
        except ImportError as e:
            logger.warning(f"Could not import existing feature engineering: {str(e)}")
            logger.info("Using simplified feature engineering...")
            return self._simplified_feature_engineering(data)
        except Exception as e:
            logger.error(f"Error in feature engineering: {str(e)}")
            return data
    
    def _simplified_feature_engineering(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Simplified feature engineering when existing modules are not available.
        
        Args:
            data: DataFrame with basic features
        
        Returns:
            DataFrame with essential features
        """
        try:
            df = data.copy()
            
            # Lag features
            for lag in [1, 2, 3, 7, 14, 30]:
                df[f'price_lag_{lag}'] = df['close'].shift(lag)
                df[f'return_lag_{lag}'] = df['daily_return'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
            
            # Rolling features
            for window in [7, 14, 21, 30, 50, 100]:
                df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
                df[f'std_{window}'] = df['close'].rolling(window=window).std()
                df[f'min_{window}'] = df['close'].rolling(window=window).min()
                df[f'max_{window}'] = df['close'].rolling(window=window).max()
                
                # Bollinger Bands
                df[f'bb_upper_{window}'] = df[f'sma_{window}'] + (2 * df[f'std_{window}'])
                df[f'bb_lower_{window}'] = df[f'sma_{window}'] - (2 * df[f'std_{window}'])
            
            # RSI
            def calculate_rsi(prices, window=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi
            
            df['rsi_14'] = calculate_rsi(df['close'], 14)
            
            # MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            
            # Time features with trigonometric encoding
            df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
            df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
            df['day_of_week_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
            df['day_of_week_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
            df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
            df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
            
            logger.info("✓ Simplified feature engineering applied")
            return df
            
        except Exception as e:
            logger.error(f"Error in simplified feature engineering: {str(e)}")
            return data
    
    def get_prediction_features(self, lookback_days: int = 100) -> pd.DataFrame:
        """
        Get the latest features ready for model prediction.
        
        Args:
            lookback_days: Number of days to look back for feature calculation
        
        Returns:
            DataFrame with features for the latest data point
        """
        try:
            # Get recent data for feature calculation
            raw_data = self.get_latest_data(period=f"{lookback_days + 50}d")
            
            if raw_data.empty:
                logger.error("No data available for feature calculation")
                return pd.DataFrame()
            
            # Calculate basic features
            data_with_basic = self.calculate_basic_features(raw_data)
            
            # Apply feature engineering
            data_with_features = self.apply_existing_feature_engineering(data_with_basic)
            
            # Get the latest row (most recent data point)
            latest_features = data_with_features.iloc[[-1]].copy()
            
            # Remove target columns if present
            target_cols = [col for col in latest_features.columns if col.startswith('target_')]
            if target_cols:
                latest_features = latest_features.drop(columns=target_cols)
            
            # Ensure we have the expected feature columns
            if self.feature_columns:
                missing_cols = set(self.feature_columns) - set(latest_features.columns)
                if missing_cols:
                    logger.warning(f"Missing feature columns: {missing_cols}")
                    # Add missing columns with NaN values
                    for col in missing_cols:
                        latest_features[col] = np.nan
                
                # Reorder columns to match expected order
                feature_cols_available = [col for col in self.feature_columns if col in latest_features.columns]
                latest_features = latest_features[feature_cols_available]
            
            logger.info(f"✓ Prediction features prepared: {len(latest_features.columns)} features")
            return latest_features
            
        except Exception as e:
            logger.error(f"Error preparing prediction features: {str(e)}")
            return pd.DataFrame()
    
    def get_data_summary(self) -> Dict[str, Any]:
        """Get summary of the data pipeline status."""
        try:
            # Get latest data sample
            sample_data = self.get_latest_data(period="5d")
            
            summary = {
                'symbol': self.symbol,
                'pipeline_status': 'operational',
                'latest_data_available': not sample_data.empty,
                'latest_date': sample_data.index[-1].strftime('%Y-%m-%d') if not sample_data.empty else 'N/A',
                'data_points_available': len(sample_data) if not sample_data.empty else 0,
                'feature_columns_count': len(self.feature_columns) if self.feature_columns else 0,
                'existing_data_path': self.existing_data_path,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if not sample_data.empty:
                summary['latest_price'] = float(sample_data['close'].iloc[-1])
                summary['price_change_24h'] = float(sample_data['daily_return'].iloc[-1]) if 'daily_return' in sample_data.columns else 0.0
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating data summary: {str(e)}")
            return {
                'symbol': self.symbol,
                'pipeline_status': 'error',
                'error': str(e),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
