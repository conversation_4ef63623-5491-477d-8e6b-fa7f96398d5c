# Bitcoin Price Prediction Production Configuration
# Integrated with existing trained models and data pipeline

# Data Collection Settings
data:
  # Primary data source (matching existing setup)
  primary_source: "yahoo"
  symbol: "BTC-USD"

  # Date range for historical data (matching existing config)
  start_date: "2019-01-01"
  end_date: null  # null for current date
  frequency: "1d"

  # Data paths (integrated with existing structure)
  paths:
    raw: "../data/raw"
    processed: "../data/processed"
    splits: "../data/splits"
    existing_processed: "../data/processed/bitcoin_preprocessed.csv"

  # Cache and backup settings
  cache_enabled: true
  backup_sources:
    - "yahoo_finance"
    - "coingecko"
    - "alphavantage"

  # API settings (from existing config)
  apis:
    yahoo:
      rate_limit: 2000  # requests per hour
    coingecko:
      rate_limit: 50    # requests per minute (free tier)
      base_url: "https://api.coingecko.com/api/v3"
    alphavantage:
      rate_limit: 5     # requests per minute (free tier)
      base_url: "https://www.alphavantage.co/query"

# Feature Engineering Settings (matching existing production setup)
features:
  # Lag features (from existing feature_engineering.py)
  price_lags: [1, 2, 3, 7, 14, 30]
  volume_lags: [1, 7, 30]
  return_lags: [1, 2, 3, 7, 14, 30]

  # Rolling windows (matching existing setup)
  rolling_windows: [7, 14, 21, 30, 50, 100]

  # Technical indicators (from existing config)
  technical_indicators:
    rsi:
      periods: [14, 21]
    bollinger_bands:
      periods: [20]
      std_dev: 2
    macd:
      fast_period: 12
      slow_period: 26
      signal_period: 9
    stochastic:
      k_period: 14
      d_period: 3
    williams_r:
      period: 14
    cci:
      period: 20

  # Volatility measures (from existing setup)
  volatility:
    rolling_windows: [7, 14, 30, 60, 90]
    parkinson_vol: 30
    gk_vol: 30

  # Time-based features (matching existing)
  time_features:
    - year
    - month
    - day_of_week
    - day_of_year
    - quarter
    - semester
    - is_weekend
    - is_month_end
    - is_quarter_end
    - month_sin
    - month_cos
    - day_of_week_sin
    - day_of_week_cos
    - day_of_year_sin
    - day_of_year_cos

  # Target variables (matching existing)
  targets:
    - target_price_1d
    - target_return_1d
    - target_direction_1d
    - target_price_7d
    - target_return_7d
    - target_direction_7d
    - target_price_30d
    - target_return_30d
    - target_direction_30d

  # Time-based features (matching existing)
  time_features:
    - year
    - month
    - day_of_week
    - day_of_year
    - quarter
    - semester
    - is_weekend
    - is_month_end
    - is_quarter_end
    - month_sin
    - month_cos
    - day_of_week_sin
    - day_of_week_cos
    - day_of_year_sin
    - day_of_year_cos

  # Target variables (matching existing)
  targets:
    - target_price_1d
    - target_return_1d
    - target_direction_1d
    - target_price_7d
    - target_return_7d
    - target_direction_7d
    - target_price_30d
    - target_return_30d
    - target_direction_30d

# Model Settings (Production - using existing trained models)
models:
  # Existing trained model paths
  model_paths:
    arima_price: "../models/arima_price_model.joblib"
    arima_returns: "../models/arima_returns_model.joblib"
    random_forest_1d: "../models/randomforest_target_return_1d_model.joblib"
    random_forest_7d: "../models/randomforest_target_return_7d_model.joblib"
    random_forest_30d: "../models/randomforest_target_return_30d_model.joblib"
    xgboost_1d: "../models/xgboost_target_return_1d_model.joblib"
    xgboost_7d: "../models/xgboost_target_return_7d_model.joblib"
    xgboost_30d: "../models/xgboost_target_return_30d_model.joblib"
    lightgbm_1d: "../models/lightgbm_target_return_1d_model.joblib"
    lightgbm_7d: "../models/lightgbm_target_return_7d_model.joblib"
    lightgbm_30d: "../models/lightgbm_target_return_30d_model.joblib"
    scaler: "../models/scaler_standard.joblib"

  # Model performance results (from existing analysis)
  results_paths:
    arima_results: "../results/arima_results.json"
    tree_models_results: "../results/tree_models_results.json"

  # ARIMA settings (matching existing config)
  arima:
    max_p: 5
    max_d: 2
    max_q: 5
    seasonal: true
    m: 7  # weekly seasonality

  # Random Forest settings (from existing hyperparameter tuning)
  random_forest:
    n_estimators: 300
    max_depth: 10
    min_samples_split: 2
    min_samples_leaf: 4
    random_state: 42

  # XGBoost settings (from existing hyperparameter tuning)
  xgboost:
    n_estimators: 100
    max_depth: 3
    learning_rate: 0.01
    subsample: 0.8
    colsample_bytree: 0.8
    random_state: 42

  # LightGBM settings (from existing hyperparameter tuning)
  lightgbm:
    n_estimators: 100
    max_depth: 3
    learning_rate: 0.01
    subsample: 0.8
    colsample_bytree: 0.8
    num_leaves: 31
    random_state: 42
  
  xgboost:
    n_estimators: 100
    max_depth: 6
    learning_rate: 0.1
    subsample: 0.8
    colsample_bytree: 0.8
    random_state: 42
  
  lightgbm:
    n_estimators: 100
    max_depth: -1
    learning_rate: 0.1
    num_leaves: 31
    subsample: 0.8
    colsample_bytree: 0.8
    random_state: 42
  
  svr:
    kernel: "rbf"
    C: 1.0
    gamma: "scale"
    epsilon: 0.1
  
  neural_network:
    hidden_layer_sizes: [100, 50]
    activation: "relu"
    learning_rate_init: 0.001
    max_iter: 500
    random_state: 42

# Ensemble Settings
ensemble:
  weights:
    arima: 0.1
    random_forest: 0.2
    xgboost: 0.3
    lightgbm: 0.25
    svr: 0.1
    neural_network: 0.05
  
  selection_criteria:
    mape_weight: -0.3
    directional_accuracy_weight: 0.2
    sharpe_ratio_weight: 0.3
    max_drawdown_weight: -0.2

# Evaluation Settings
evaluation:
  test_size: 0.2
  cv_folds: 5
  metrics:
    - "MAE"
    - "RMSE"
    - "MAPE"
    - "R2"
    - "Directional_Accuracy"
    - "Sharpe_Ratio"
    - "Max_Drawdown"
    - "Calmar_Ratio"
  
  risk_free_rate: 0.02
  trading_days: 252

# Visualization Settings
visualization:
  style: "seaborn-v0_8"
  figsize: [12, 8]
  colors:
    bitcoin: "#F7931A"
    prediction: "#1f77b4"
    actual: "#ff7f0e"
    error: "#d62728"
    profit: "#2ca02c"
    loss: "#d62728"
  
  plotly_template: "plotly_white"
  save_format: "png"
  dpi: 300

# Output Settings
output:
  results_dir: "results"
  figures_dir: "results/figures"
  reports_dir: "results/reports"
  models_dir: "models/trained"
  
  save_models: true
  save_predictions: true
  save_figures: true
  generate_report: true

# Logging Settings
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/bitcoin_prediction.log"

# Performance Settings
performance:
  n_jobs: -1  # Use all available cores
  memory_limit: "8GB"
  cache_size: "1GB"
  
# Alert Settings
alerts:
  performance_threshold:
    mape_max: 10.0
    directional_accuracy_min: 60.0
    sharpe_ratio_min: 0.5
  
  email_notifications: false
  slack_notifications: false
