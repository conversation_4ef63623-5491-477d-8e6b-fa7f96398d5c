"""
Bitcoin Prediction Visualization Module

This module provides comprehensive visualization functions for:
- Price trends vs predictions
- Error distribution analysis  
- Feature importance charts
- Interactive dashboards
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class BitcoinVisualization:
    """
    Comprehensive visualization class for Bitcoin prediction analysis
    """
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        self.figsize = figsize
        self.colors = {
            'bitcoin': '#F7931A',
            'prediction': '#1f77b4',
            'actual': '#ff7f0e',
            'error': '#d62728',
            'profit': '#2ca02c',
            'loss': '#d62728'
        }
    
    def plot_price_vs_predictions(self, 
                                 actual: pd.Series, 
                                 predictions: pd.Series,
                                 confidence_intervals: Optional[Dict] = None,
                                 title: str = "Bitcoin Price vs Predictions") -> go.Figure:
        """
        Create interactive plot comparing actual prices with predictions
        
        Args:
            actual: Actual price series
            predictions: Predicted price series
            confidence_intervals: Dict with 'lower' and 'upper' bounds
            title: Plot title
            
        Returns:
            Plotly figure object
        """
        fig = go.Figure()
        
        # Add actual prices
        fig.add_trace(go.Scatter(
            x=actual.index,
            y=actual.values,
            mode='lines',
            name='Actual Price',
            line=dict(color=self.colors['actual'], width=2)
        ))
        
        # Add predictions
        fig.add_trace(go.Scatter(
            x=predictions.index,
            y=predictions.values,
            mode='lines',
            name='Predicted Price',
            line=dict(color=self.colors['prediction'], width=2, dash='dash')
        ))
        
        # Add confidence intervals if provided
        if confidence_intervals:
            fig.add_trace(go.Scatter(
                x=predictions.index,
                y=confidence_intervals['upper'],
                mode='lines',
                line=dict(width=0),
                showlegend=False,
                hoverinfo='skip'
            ))
            
            fig.add_trace(go.Scatter(
                x=predictions.index,
                y=confidence_intervals['lower'],
                mode='lines',
                line=dict(width=0),
                name='Confidence Interval',
                fill='tonexty',
                fillcolor='rgba(31, 119, 180, 0.2)',
                hoverinfo='skip'
            ))
        
        fig.update_layout(
            title=title,
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            template='plotly_white',
            height=600,
            hovermode='x unified'
        )
        
        return fig
    
    def plot_error_distribution(self, 
                               errors: np.ndarray,
                               model_name: str = "Model") -> plt.Figure:
        """
        Create comprehensive error distribution analysis
        
        Args:
            errors: Array of prediction errors
            model_name: Name of the model for title
            
        Returns:
            Matplotlib figure object
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'{model_name} - Error Distribution Analysis', fontsize=16, fontweight='bold')
        
        # Histogram of errors
        axes[0, 0].hist(errors, bins=50, alpha=0.7, color=self.colors['error'], edgecolor='black')
        axes[0, 0].axvline(np.mean(errors), color='red', linestyle='--', linewidth=2, label=f'Mean: {np.mean(errors):.2f}')
        axes[0, 0].axvline(np.median(errors), color='green', linestyle='--', linewidth=2, label=f'Median: {np.median(errors):.2f}')
        axes[0, 0].set_title('Error Distribution')
        axes[0, 0].set_xlabel('Prediction Error')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Q-Q plot for normality check
        from scipy import stats
        stats.probplot(errors, dist="norm", plot=axes[0, 1])
        axes[0, 1].set_title('Q-Q Plot (Normality Check)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Box plot
        box_plot = axes[1, 0].boxplot(errors, patch_artist=True)
        box_plot['boxes'][0].set_facecolor(self.colors['error'])
        box_plot['boxes'][0].set_alpha(0.7)
        axes[1, 0].set_title('Error Box Plot')
        axes[1, 0].set_ylabel('Prediction Error')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Error statistics
        stats_text = f"""
        Error Statistics:
        Mean: {np.mean(errors):.4f}
        Std: {np.std(errors):.4f}
        Median: {np.median(errors):.4f}
        MAE: {np.mean(np.abs(errors)):.4f}
        RMSE: {np.sqrt(np.mean(errors**2)):.4f}
        Min: {np.min(errors):.4f}
        Max: {np.max(errors):.4f}
        Skewness: {stats.skew(errors):.4f}
        Kurtosis: {stats.kurtosis(errors):.4f}
        """
        
        axes[1, 1].text(0.1, 0.9, stats_text, transform=axes[1, 1].transAxes, 
                        fontsize=10, verticalalignment='top', fontfamily='monospace',
                        bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        axes[1, 1].set_title('Error Statistics')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        return fig
    
    def plot_feature_importance(self, 
                               importance_scores: Dict[str, float],
                               title: str = "Feature Importance",
                               top_n: int = 20) -> go.Figure:
        """
        Create interactive feature importance chart
        
        Args:
            importance_scores: Dictionary of feature names and importance scores
            title: Plot title
            top_n: Number of top features to display
            
        Returns:
            Plotly figure object
        """
        # Sort features by importance
        sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)[:top_n]
        features, scores = zip(*sorted_features)
        
        # Create color scale based on importance
        colors = px.colors.sequential.Viridis
        color_scale = [colors[int(i * (len(colors) - 1) / (len(scores) - 1))] for i in range(len(scores))]
        
        fig = go.Figure(data=go.Bar(
            x=list(scores),
            y=list(features),
            orientation='h',
            marker=dict(
                color=color_scale,
                line=dict(color='black', width=1)
            ),
            text=[f'{score:.3f}' for score in scores],
            textposition='auto'
        ))
        
        fig.update_layout(
            title=title,
            xaxis_title='Importance Score',
            yaxis_title='Features',
            template='plotly_white',
            height=max(400, len(features) * 25),
            yaxis=dict(autorange='reversed')
        )
        
        return fig
    
    def create_performance_dashboard(self, 
                                   results: Dict[str, Dict],
                                   metrics: List[str] = ['MAPE', 'RMSE', 'Directional_Accuracy']) -> go.Figure:
        """
        Create comprehensive model performance comparison dashboard
        
        Args:
            results: Dictionary with model names as keys and metrics as values
            metrics: List of metrics to compare
            
        Returns:
            Plotly figure with subplots
        """
        models = list(results.keys())
        n_metrics = len(metrics)
        
        fig = make_subplots(
            rows=1, cols=n_metrics,
            subplot_titles=metrics,
            specs=[[{"type": "bar"} for _ in range(n_metrics)]]
        )
        
        colors = px.colors.qualitative.Set3[:len(models)]
        
        for i, metric in enumerate(metrics):
            values = [results[model].get(metric, 0) for model in models]
            
            fig.add_trace(
                go.Bar(
                    x=models,
                    y=values,
                    name=metric,
                    marker_color=colors,
                    text=[f'{v:.3f}' for v in values],
                    textposition='auto',
                    showlegend=(i == 0)
                ),
                row=1, col=i+1
            )
        
        fig.update_layout(
            title='Model Performance Comparison Dashboard',
            template='plotly_white',
            height=500,
            showlegend=False
        )
        
        return fig
    
    def plot_prediction_intervals(self, 
                                 data: pd.DataFrame,
                                 prediction_col: str,
                                 actual_col: str,
                                 lower_bound_col: str,
                                 upper_bound_col: str,
                                 title: str = "Prediction Intervals") -> go.Figure:
        """
        Plot predictions with confidence/prediction intervals
        
        Args:
            data: DataFrame containing predictions and intervals
            prediction_col: Column name for predictions
            actual_col: Column name for actual values
            lower_bound_col: Column name for lower bounds
            upper_bound_col: Column name for upper bounds
            title: Plot title
            
        Returns:
            Plotly figure object
        """
        fig = go.Figure()
        
        # Add prediction intervals
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data[upper_bound_col],
            mode='lines',
            line=dict(width=0),
            showlegend=False,
            hoverinfo='skip'
        ))
        
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data[lower_bound_col],
            mode='lines',
            line=dict(width=0),
            name='Prediction Interval',
            fill='tonexty',
            fillcolor='rgba(31, 119, 180, 0.2)',
            hoverinfo='skip'
        ))
        
        # Add actual values
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data[actual_col],
            mode='lines+markers',
            name='Actual',
            line=dict(color=self.colors['actual'], width=2),
            marker=dict(size=4)
        ))
        
        # Add predictions
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data[prediction_col],
            mode='lines+markers',
            name='Predicted',
            line=dict(color=self.colors['prediction'], width=2, dash='dash'),
            marker=dict(size=4)
        ))
        
        fig.update_layout(
            title=title,
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            template='plotly_white',
            height=600,
            hovermode='x unified'
        )
        
        return fig
    
    def save_figure(self, fig, filename: str, format: str = 'png', width: int = 1200, height: int = 800):
        """
        Save plotly figure to file
        
        Args:
            fig: Plotly figure object
            filename: Output filename
            format: File format ('png', 'jpg', 'pdf', 'svg', 'html')
            width: Image width in pixels
            height: Image height in pixels
        """
        if format == 'html':
            fig.write_html(filename)
        else:
            fig.write_image(filename, format=format, width=width, height=height)
        
        print(f"Figure saved as {filename}")


def create_summary_report(results: Dict[str, Dict], 
                         output_path: str = "../results/reports/model_summary.html"):
    """
    Create comprehensive HTML summary report
    
    Args:
        results: Dictionary with model results
        output_path: Path to save the HTML report
    """
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Bitcoin Prediction Model Summary</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .header {{ background-color: #f7931a; color: white; padding: 20px; text-align: center; }}
            .metric {{ background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }}
            .model {{ border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 Bitcoin Price Prediction Model Summary</h1>
            <p>Generated on {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <h2>📊 Model Performance Comparison</h2>
        <table>
            <tr>
                <th>Model</th>
                <th>MAPE (%)</th>
                <th>RMSE</th>
                <th>Directional Accuracy (%)</th>
                <th>Sharpe Ratio</th>
            </tr>
    """
    
    for model_name, metrics in results.items():
        html_content += f"""
            <tr>
                <td><strong>{model_name}</strong></td>
                <td>{metrics.get('MAPE', 'N/A')}</td>
                <td>{metrics.get('RMSE', 'N/A')}</td>
                <td>{metrics.get('Directional_Accuracy', 'N/A')}</td>
                <td>{metrics.get('Sharpe_Ratio', 'N/A')}</td>
            </tr>
        """
    
    html_content += """
        </table>
        
        <h2>🎯 Key Insights</h2>
        <div class="metric">
            <h3>Best Performing Model</h3>
            <p>Based on comprehensive evaluation metrics...</p>
        </div>
        
        <div class="metric">
            <h3>Feature Importance</h3>
            <p>Most influential features for Bitcoin price prediction...</p>
        </div>
        
        <div class="metric">
            <h3>Risk Analysis</h3>
            <p>Risk-adjusted returns and volatility considerations...</p>
        </div>
        
        <h2>📚 Lessons Learned</h2>
        <ul>
            <li>Market regime considerations are crucial for model performance</li>
            <li>Ensemble methods often outperform individual models</li>
            <li>Feature engineering significantly impacts prediction accuracy</li>
            <li>Risk management is essential for practical implementation</li>
        </ul>
        
    </body>
    </html>
    """
    
    with open(output_path, 'w') as f:
        f.write(html_content)
    
    print(f"Summary report saved to {output_path}")


if __name__ == "__main__":
    # Example usage
    viz = BitcoinVisualization()
    
    # Create sample data for demonstration
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    actual = pd.Series(np.random.randn(100).cumsum() + 50000, index=dates)
    predicted = actual + np.random.randn(100) * 1000
    
    # Create visualizations
    price_fig = viz.plot_price_vs_predictions(actual, predicted)
    price_fig.show()
    
    errors = actual - predicted
    error_fig = viz.plot_error_distribution(errors.values, "Sample Model")
    plt.show()
    
    # Feature importance example
    features = {'RSI': 0.15, 'MACD': 0.12, 'BB_Position': 0.10, 'Volume': 0.08}
    importance_fig = viz.plot_feature_importance(features)
    importance_fig.show()
