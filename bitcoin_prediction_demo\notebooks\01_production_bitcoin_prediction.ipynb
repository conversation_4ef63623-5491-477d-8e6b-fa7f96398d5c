{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Bitcoin Price Prediction - Production System\n",
    "\n",
    "This notebook demonstrates a **production-level** Bitcoin price prediction system that integrates with existing trained models, processed data, and proven architecture from the original project.\n",
    "\n",
    "## Production Features\n",
    "- **Pre-trained Models**: Utilizes existing ARIMA, Random Forest, XGBoost, LightGBM models\n",
    "- **Real-time Data Pipeline**: Live data fetching and feature engineering\n",
    "- **Model Performance Integration**: Leverages existing model evaluation results\n",
    "- **Production API**: REST endpoints for real-time predictions\n",
    "- **Ensemble Predictions**: Combines multiple models for robust forecasts\n",
    "- **Monitoring & Health Checks**: System status and model performance tracking\n",
    "\n",
    "## System Architecture\n",
    "- **Production Model Manager**: Loads and manages pre-trained models\n",
    "- **Data Pipeline**: Real-time data processing with existing feature engineering\n",
    "- **Prediction API**: RESTful service for model inference\n",
    "- **Monitoring Dashboard**: System health and prediction visualization\n",
    "\n",
    "## Notebook Structure\n",
    "1. **System Overview & Integration**\n",
    "2. **Production Model Loading**\n",
    "3. **Real-time Data Pipeline**\n",
    "4. **Live Predictions Demo**\n",
    "5. **Model Performance Analysis**\n",
    "6. **Production API Demo**\n",
    "7. **System Monitoring & Health**\n",
    "8. **Production Deployment Guide**"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. System Overview & Integration\n",
    "\n",
    "Let's start by examining the existing production assets and integrating them into our system."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import sys\n",
    "import os\n",
    "from pathlib import Path\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import warnings\n",
    "import json\n",
    "from datetime import datetime, timedelta\n",
    "\n",
    "# Suppress warnings for cleaner output\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set up plotting style\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "# Add src directory to path\n",
    "sys.path.append('../src')\n",
    "\n",
    "print(\"📊 Production Bitcoin Prediction System\")\n",
    "print(\"🚀 Integrating with existing trained models and data\")\n",
    "print(f\"📅 Notebook executed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### Examine Existing Production Assets"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check existing models and data\n",
    "print(\"🔍 Examining existing production assets...\\n\")\n",
    "\n",
    "# Models directory\n",
    "models_dir = Path(\"../../models\")\n",
    "if models_dir.exists():\n",
    "    model_files = list(models_dir.glob(\"*.joblib\"))\n",
    "    print(f\"📁 Found {len(model_files)} trained models:\")\n",
    "    for model_file in sorted(model_files):\n",
    "        size_mb = model_file.stat().st_size / (1024 * 1024)\n",
    "        print(f\"   ✓ {model_file.name} ({size_mb:.1f} MB)\")\n",
    "else:\n",
    "    print(\"❌ Models directory not found\")\n",
    "\n",
    "print()\n",
    "\n",
    "# Results directory\n",
    "results_dir = Path(\"../../results\")\n",
    "if results_dir.exists():\n",
    "    result_files = list(results_dir.glob(\"*.json\"))\n",
    "    print(f\"📊 Found {len(result_files)} result files:\")\n",
    "    for result_file in sorted(result_files):\n",
    "        print(f\"   ✓ {result_file.name}\")\n",
    "else:\n",
    "    print(\"❌ Results directory not found\")\n",
    "\n",
    "print()\n",
    "\n",
    "# Data directory\n",
    "data_dir = Path(\"../../data/processed\")\n",
    "if data_dir.exists():\n",
    "    data_files = list(data_dir.glob(\"*.csv\"))\n",
    "    print(f\"📈 Found {len(data_files)} processed data files:\")\n",
    "    for data_file in sorted(data_files):\n",
    "        size_mb = data_file.stat().st_size / (1024 * 1024)\n",
    "        print(f\"   ✓ {data_file.name} ({size_mb:.1f} MB)\")\n",
    "else:\n",
    "    print(\"❌ Processed data directory not found\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Production Model Loading\n",
    "\n",
    "Initialize our production model manager to load and manage all pre-trained models."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Initialize Production Model Manager\n",
    "try:\n",
    "    from production_model_manager import ProductionModelManager\n",
    "    \n",
    "    print(\"🔧 Initializing Production Model Manager...\")\n",
    "    model_manager = ProductionModelManager()\n",
    "    \n",
    "    # Get model summary\n",
    "    summary = model_manager.get_model_summary()\n",
    "    \n",
    "    print(f\"\\n✅ Model Manager initialized successfully!\")\n",
    "    print(f\"📊 Loaded {summary['model_count']} models\")\n",
    "    print(f\"🔢 Feature count: {summary['feature_count']}\")\n",
    "    print(f\"⚖️ Scaler loaded: {summary['scaler_loaded']}\")\n",
    "    print(f\"📈 Results loaded: {summary['results_loaded']} files\")\n",
    "    \n",
    "    print(\"\\n🤖 Available models:\")\n",
    "    for model_name in summary['loaded_models']:\n",
    "        print(f\"   ✓ {model_name}\")\n",
    "        \n",
    "except Exception as e:\n",
    "    print(f\"❌ Error initializing model manager: {str(e)}\")\n",
    "    model_manager = None"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### Model Performance Overview"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display model performance summary\n",
    "if model_manager and 'performance_summary' in summary:\n",
    "    print(\"📊 Model Performance Summary\\n\")\n",
    "    \n",
    "    perf_df_data = []\n",
    "    for timeframe, models in summary['performance_summary'].items():\n",
    "        for model_name, metrics in models.items():\n",
    "            perf_df_data.append({\n",
    "                'Timeframe': timeframe,\n",
    "                'Model': model_name.title(),\n",
    "                'RMSE': f\"{metrics['rmse']:.4f}\" if isinstance(metrics['rmse'], (int, float)) else metrics['rmse'],\n",
    "                'R²': f\"{metrics['r2']:.4f}\" if isinstance(metrics['r2'], (int, float)) else metrics['r2'],\n",
    "                'Directional Accuracy': f\"{metrics['directional_accuracy']:.2f}%\" if isinstance(metrics['directional_accuracy'], (int, float)) else metrics['directional_accuracy']\n",
    "            })\n",
    "    \n",
    "    if perf_df_data:\n",
    "        perf_df = pd.DataFrame(perf_df_data)\n",
    "        print(perf_df.to_string(index=False))\n",
    "        \n",
    "        # Create performance visualization\n",
    "        fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n",
    "        \n",
    "        # RMSE comparison\n",
    "        rmse_data = perf_df[perf_df['RMSE'] != 'N/A'].copy()\n",
    "        if not rmse_data.empty:\n",
    "            rmse_data['RMSE_numeric'] = rmse_data['RMSE'].astype(float)\n",
    "            rmse_pivot = rmse_data.pivot(index='Model', columns='Timeframe', values='RMSE_numeric')\n",
    "            rmse_pivot.plot(kind='bar', ax=axes[0], title='RMSE by Model and Timeframe')\n",
    "            axes[0].set_ylabel('RMSE')\n",
    "            axes[0].tick_params(axis='x', rotation=45)\n",
    "        \n",
    "        # R² comparison\n",
    "        r2_data = perf_df[perf_df['R²'] != 'N/A'].copy()\n",
    "        if not r2_data.empty:\n",
    "            r2_data['R2_numeric'] = r2_data['R²'].astype(float)\n",
    "            r2_pivot = r2_data.pivot(index='Model', columns='Timeframe', values='R2_numeric')\n",
    "            r2_pivot.plot(kind='bar', ax=axes[1], title='R² Score by Model and Timeframe')\n",
    "            axes[1].set_ylabel('R² Score')\n",
    "            axes[1].tick_params(axis='x', rotation=45)\n",
    "        \n",
    "        # Directional Accuracy comparison\n",
    "        da_data = perf_df[perf_df['Directional Accuracy'] != 'N/A'].copy()\n",
    "        if not da_data.empty:\n",
    "            da_data['DA_numeric'] = da_data['Directional Accuracy'].str.rstrip('%').astype(float)\n",
    "            da_pivot = da_data.pivot(index='Model', columns='Timeframe', values='DA_numeric')\n",
    "            da_pivot.plot(kind='bar', ax=axes[2], title='Directional Accuracy by Model and Timeframe')\n",
    "            axes[2].set_ylabel('Directional Accuracy (%)')\n",
    "            axes[2].tick_params(axis='x', rotation=45)\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "else:\n",
    "    print(\"⚠️ Performance summary not available\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Real-time Data Pipeline\n",
    "\n",
    "Initialize the data pipeline for real-time data processing and feature engineering."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Initialize Production Data Pipeline\n",
    "try:\n",
    "    from production_data_pipeline import ProductionDataPipeline\n",
    "    \n",
    "    print(\"🔧 Initializing Production Data Pipeline...\")\n",
    "    data_pipeline = ProductionDataPipeline()\n",
    "    \n",
    "    # Get data summary\n",
    "    data_summary = data_pipeline.get_data_summary()\n",
    "    \n",
    "    print(f\"\\n✅ Data Pipeline initialized successfully!\")\n",
    "    print(f\"📈 Symbol: {data_summary['symbol']}\")\n",
    "    print(f\"🔄 Pipeline status: {data_summary['pipeline_status']}\")\n",
    "    print(f\"📅 Latest data: {data_summary['latest_date']}\")\n",
    "    print(f\"📊 Data points: {data_summary['data_points_available']}\")\n",
    "    print(f\"🔢 Feature columns: {data_summary['feature_columns_count']}\")\n",
    "    \n",
    "    if 'latest_price' in data_summary:\n",
    "        print(f\"💰 Latest price: ${data_summary['latest_price']:,.2f}\")\n",
    "        print(f\"📈 24h change: {data_summary['price_change_24h']:.2%}\")\n",
    "        \n",
    "except Exception as e:\n",
    "    print(f\"❌ Error initializing data pipeline: {str(e)}\")\n",
    "    data_pipeline = None"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### Fetch Latest Data Sample"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Get latest Bitcoin data\n",
    "if data_pipeline:\n",
    "    print(\"📊 Fetching latest Bitcoin data...\")\n",
    "    \n",
    "    # Get recent data\n",
    "    latest_data = data_pipeline.get_latest_data(period=\"30d\")\n",
    "    \n",
    "    if not latest_data.empty:\n",
    "        print(f\"✅ Retrieved {len(latest_data)} days of data\")\n",
    "        print(f\"📅 Date range: {latest_data.index[0].strftime('%Y-%m-%d')} to {latest_data.index[-1].strftime('%Y-%m-%d')}\")\n",
    "        \n",
    "        # Display recent price action\n",
    "        print(\"\\n📈 Recent Price Action (Last 5 days):\")\n",
    "        recent_data = latest_data.tail(5)[['open', 'high', 'low', 'close', 'volume']]\n",
    "        recent_data['daily_return'] = recent_data['close'].pct_change()\n",
    "        \n",
    "        display_data = recent_data.copy()\n",
    "        for col in ['open', 'high', 'low', 'close']:\n",
    "            display_data[col] = display_data[col].apply(lambda x: f\"${x:,.2f}\")\n",
    "        display_data['volume'] = display_data['volume'].apply(lambda x: f\"{x:,.0f}\")\n",
    "        display_data['daily_return'] = display_data['daily_return'].apply(lambda x: f\"{x:.2%}\" if pd.notna(x) else \"N/A\")\n",
    "        \n",
    "        print(display_data.to_string())\n",
    "        \n",
    "        # Plot recent price trend\n",
    "        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))\n",
    "        \n",
    "        # Price chart\n",
    "        ax1.plot(latest_data.index, latest_data['close'], linewidth=2, label='Close Price')\n",
    "        ax1.fill_between(latest_data.index, latest_data['low'], latest_data['high'], alpha=0.3, label='Daily Range')\n",
    "        ax1.set_title('Bitcoin Price - Last 30 Days', fontsize=14, fontweight='bold')\n",
    "        ax1.set_ylabel('Price (USD)')\n",
    "        ax1.legend()\n",
    "        ax1.grid(True, alpha=0.3)\n",
    "        \n",
    "        # Volume chart\n",
    "        ax2.bar(latest_data.index, latest_data['volume'], alpha=0.7, color='orange')\n",
    "        ax2.set_title('Trading Volume - Last 30 Days', fontsize=14, fontweight='bold')\n",
    "        ax2.set_ylabel('Volume')\n",
    "        ax2.grid(True, alpha=0.3)\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "    else:\n",
    "        print(\"❌ No data retrieved\")\n",
    "else:\n",
    "    print(\"⚠️ Data pipeline not available\")"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Live Predictions Demo\n",
    "\n",
    "Now let's demonstrate live predictions using our production system."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Generate live predictions\n",
    "if model_manager and data_pipeline:\n",
    "    print(\"🔮 Generating Live Bitcoin Predictions...\\n\")\n",
    "    \n",
    "    # ARIMA Predictions\n",
    "    print(\"📊 ARIMA Time Series Predictions:\")\n",
    "    try:\n",
    "        arima_pred = model_manager.predict_arima(steps=7)\n",
    "        if arima_pred:\n",
    "            print(f\"✅ Generated {len(arima_pred['forecast_dates'])} day forecasts\")\n",
    "            \n",
    "            # Display ARIMA predictions\n",
    "            arima_df = pd.DataFrame({\n",
    "                'Date': arima_pred['forecast_dates'],\n",
    "                'Price_Forecast': arima_pred['price_forecast'],\n",
    "                'Return_Forecast': arima_pred['return_forecast']\n",
    "            })\n",
    "            print(arima_df.to_string(index=False))\n",
    "        else:\n",
    "            print(\"❌ ARIMA prediction failed\")\n",
    "    except Exception as e:\n",
    "        print(f\"❌ ARIMA error: {str(e)}\")\n",
    "    \n",
    "    print(\"\\n\" + \"=\"*50 + \"\\n\")\n",
    "    \n",
    "    # Tree Model Ensemble Predictions\n",
    "    print(\"🌳 Tree Model Ensemble Predictions:\")\n",
    "    \n",
    "    # Get prediction features\n",
    "    features = data_pipeline.get_prediction_features()\n",
    "    \n",
    "    if not features.empty:\n",
    "        print(f\"✅ Prepared {len(features.columns)} features for prediction\")\n",
    "        \n",
    "        # Generate predictions for all timeframes\n",
    "        timeframes = ['1d', '7d', '30d']\n",
    "        ensemble_results = {}\n",
    "        \n",
    "        for timeframe in timeframes:\n",
    "            try:\n",
    "                ensemble_pred = model_manager.get_ensemble_prediction(features, timeframe)\n",
    "                if ensemble_pred:\n",
    "                    ensemble_results[timeframe] = ensemble_pred\n",
    "                    print(f\"\\n📈 {timeframe.upper()} Predictions:\")\n",
    "                    print(f\"   Ensemble: {ensemble_pred['ensemble_prediction'][0]:.4f}\")\n",
    "                    print(f\"   Confidence: {ensemble_pred['prediction_confidence'][0]:.4f}\")\n",
    "                    print(f\"   Models used: {ensemble_pred['model_count']}\")\n",
    "                    \n",
    "                    # Show individual model predictions\n",
    "                    print(\"   Individual predictions:\")\n",
    "                    for model, pred in ensemble_pred['individual_predictions'].items():\n",
    "                        print(f\"     {model.title()}: {pred[0]:.4f}\")\n",
    "                else:\n",
    "                    print(f\"❌ {timeframe} prediction failed\")\n",
    "            except Exception as e:\n",
    "                print(f\"❌ {timeframe} error: {str(e)}\")\n",
    "        \n",
    "        # Visualize ensemble predictions\n",
    "        if ensemble_results:\n",
    "            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "            \n",
    "            # Ensemble predictions comparison\n",
    "            timeframes_plot = list(ensemble_results.keys())\n",
    "            ensemble_preds = [ensemble_results[tf]['ensemble_prediction'][0] for tf in timeframes_plot]\n",
    "            confidences = [ensemble_results[tf]['prediction_confidence'][0] for tf in timeframes_plot]\n",
    "            \n",
    "            bars1 = ax1.bar(timeframes_plot, ensemble_preds, alpha=0.7, color='skyblue')\n",
    "            ax1.set_title('Ensemble Return Predictions by Timeframe', fontweight='bold')\n",
    "            ax1.set_ylabel('Predicted Return')\n",
    "            ax1.grid(True, alpha=0.3)\n",
    "            \n",
    "            # Add value labels on bars\n",
    "            for bar, pred in zip(bars1, ensemble_preds):\n",
    "                height = bar.get_height()\n",
    "                ax1.text(bar.get_x() + bar.get_width()/2., height,\n",
    "                        f'{pred:.4f}', ha='center', va='bottom')\n",
    "            \n",
    "            # Prediction confidence\n",
    "            bars2 = ax2.bar(timeframes_plot, confidences, alpha=0.7, color='lightcoral')\n",
    "            ax2.set_title('Prediction Confidence by Timeframe', fontweight='bold')\n",
    "            ax2.set_ylabel('Confidence Score')\n",
    "            ax2.set_ylim(0, 1)\n",
    "            ax2.grid(True, alpha=0.3)\n",
    "            \n",
    "            # Add value labels on bars\n",
    "            for bar, conf in zip(bars2, confidences):\n",
    "                height = bar.get_height()\n",
    "                ax2.text(bar.get_x() + bar.get_width()/2., height,\n",
    "                        f'{conf:.3f}', ha='center', va='bottom')\n",
    "            \n",
    "            plt.tight_layout()\n",
    "            plt.show()\n",
    "    else:\n",
    "        print(\"❌ Could not prepare prediction features\")\n",
    "else:\n",
    "    print(\"⚠️ Model manager or data pipeline not available\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Production API Demo\n",
    "\n",
    "Let's demonstrate the production API capabilities."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# API Demo - Start the production API server\n",
    "print(\"🚀 Production API Demo\\n\")\n",
    "\n",
    "print(\"To start the production API server, run the following command in a terminal:\")\n",
    "print(\"\")\n",
    "print(\"cd ../src\")\n",
    "print(\"python production_prediction_api.py\")\n",
    "print(\"\")\n",
    "print(\"The API will be available at: http://localhost:5000\")\n",
    "print(\"\")\n",
    "print(\"Available endpoints:\")\n",
    "endpoints = [\n",
    "    (\"/health\", \"System health check\"),\n",
    "    (\"/models/summary\", \"Get summary of loaded models\"),\n",
    "    (\"/data/summary\", \"Get data pipeline status\"),\n",
    "    (\"/predict/arima?steps=7\", \"ARIMA predictions\"),\n",
    "    (\"/predict/tree/1d\", \"Tree model predictions (1d)\"),\n",
    "    (\"/predict/ensemble/7d\", \"Ensemble predictions (7d)\"),\n",
    "    (\"/predict/all\", \"All model predictions\"),\n",
    "    (\"/performance/random_forest?timeframe=1d\", \"Model performance metrics\")\n",
    "]\n",
    "\n",
    "for endpoint, description in endpoints:\n",
    "    print(f\"  ✓ {endpoint:<35} - {description}\")\n",
    "\n",
    "print(\"\\n💡 Example API calls:\")\n",
    "print(\"curl http://localhost:5000/health\")\n",
    "print(\"curl http://localhost:5000/predict/all\")\n",
    "print(\"curl http://localhost:5000/models/summary\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. System Monitoring & Health\n",
    "\n",
    "Monitor the health and performance of our production system."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# System Health Check\n",
    "print(\"🏥 Production System Health Check\\n\")\n",
    "\n",
    "health_status = {\n",
    "    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n",
    "    'components': {}\n",
    "}\n",
    "\n",
    "# Check Model Manager\n",
    "if model_manager:\n",
    "    try:\n",
    "        summary = model_manager.get_model_summary()\n",
    "        health_status['components']['model_manager'] = {\n",
    "            'status': '✅ Healthy',\n",
    "            'models_loaded': summary['model_count'],\n",
    "            'features': summary['feature_count'],\n",
    "            'scaler': summary['scaler_loaded']\n",
    "        }\n",
    "    except Exception as e:\n",
    "        health_status['components']['model_manager'] = {\n",
    "            'status': f'❌ Error: {str(e)}'\n",
    "        }\nelse:\n",
    "    health_status['components']['model_manager'] = {\n",
    "        'status': '❌ Not initialized'\n",
    "    }\n",
    "\n",
    "# Check Data Pipeline\n",
    "if data_pipeline:\n",
    "    try:\n",
    "        data_summary = data_pipeline.get_data_summary()\n",
    "        health_status['components']['data_pipeline'] = {\n",
    "            'status': '✅ Healthy',\n",
    "            'pipeline_status': data_summary['pipeline_status'],\n",
    "            'latest_data': data_summary['latest_date'],\n",
    "            'data_points': data_summary['data_points_available']\n",
    "        }\n",
    "    except Exception as e:\n",
    "        health_status['components']['data_pipeline'] = {\n",
    "            'status': f'❌ Error: {str(e)}'\n",
    "        }\nelse:\n",
    "    health_status['components']['data_pipeline'] = {\n",
    "        'status': '❌ Not initialized'\n",
    "    }\n",
    "\n",
    "# Display health status\n",
    "print(f\"📅 Health Check Time: {health_status['timestamp']}\\n\")\n",
    "\n",
    "for component, status in health_status['components'].items():\n",
    "    print(f\"🔧 {component.replace('_', ' ').title()}:\")\n",
    "    for key, value in status.items():\n",
    "        print(f\"   {key}: {value}\")\n",
    "    print()\n",
    "\n",
    "# Overall system status\n",
    "all_healthy = all('✅' in str(comp.get('status', '')) for comp in health_status['components'].values())\n",
    "overall_status = \"🟢 System Operational\" if all_healthy else \"🟡 System Issues Detected\"\n",
    "print(f\"🎯 Overall Status: {overall_status}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Production Deployment Guide\n",
    "\n",
    "Guidelines for deploying this system to production."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 🚀 Production Deployment Checklist\n",
    "\n",
    "#### Prerequisites\n",
    "- [ ] All trained models are available in `../models/` directory\n",
    "- [ ] Processed data is available in `../data/processed/` directory\n",
    "- [ ] Model performance results are in `../results/` directory\n",
    "- [ ] Python environment with required dependencies\n",
    "\n",
    "#### Deployment Steps\n",
    "\n",
    "1. **Environment Setup**\n",
    "   ```bash\n",
    "   # Install dependencies\n",
    "   pip install -r requirements.txt\n",
    "   \n",
    "   # Set environment variables\n",
    "   export FLASK_ENV=production\n",
    "   export FLASK_DEBUG=False\n",
    "   ```\n",
    "\n",
    "2. **Configuration**\n",
    "   - Update `config.yaml` with production settings\n",
    "   - Configure API rate limits and security\n",
    "   - Set up logging and monitoring\n",
    "\n",
    "3. **API Server Deployment**\n",
    "   ```bash\n",
    "   # Start production API server\n",
    "   cd src/\n",
    "   python production_prediction_api.py\n",
    "   \n",
    "   # Or use gunicorn for production\n",
    "   gunicorn -w 4 -b 0.0.0.0:5000 production_prediction_api:app\n",
    "   ```\n",
    "\n",
    "4. **Health Monitoring**\n",
    "   - Set up automated health checks\n",
    "   - Monitor API response times\n",
    "   - Track prediction accuracy over time\n",
    "   - Set up alerts for system failures\n",
    "\n",
    "5. **Security Considerations**\n",
    "   - Implement API authentication\n",
    "   - Set up rate limiting\n",
    "   - Use HTTPS in production\n",
    "   - Secure model files and data\n",
    "\n",
    "#### Production Architecture\n",
    "\n",
    "```\n",
    "┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐\n",
    "│   Data Sources  │───▶│  Data Pipeline   │───▶│ Feature Store   │\n",
    "│  (Yahoo Finance)│    │ (Real-time ETL)  │    │ (Processed Data)│\n",
    "└─────────────────┘    └──────────────────┘    └─────────────────┘\n",
    "                                                         │\n",
    "                                                         ▼\n",
    "┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐\n",
    "│   API Gateway   │◀───│  Prediction API  │◀───│ Model Manager   │\n",
    "│ (Load Balancer) │    │  (Flask/FastAPI) │    │(Pre-trained ML) │\n",
    "└─────────────────┘    └──────────────────┘    └─────────────────┘\n",
    "         │                       │                       │\n",
    "         ▼                       ▼                       ▼\n",
    "┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐\n",
    "│   Monitoring    │    │     Logging      │    │   Model Store   │\n",
    "│ (Prometheus)    │    │  (ELK Stack)     │    │ (Trained Models)│\n",
    "└─────────────────┘    └──────────────────┘    └─────────────────┘\n",
    "```\n",
    "\n",
    "#### Performance Optimization\n",
    "- Use model caching to reduce load times\n",
    "- Implement connection pooling for data sources\n",
    "- Use async processing for non-blocking operations\n",
    "- Consider model quantization for faster inference\n",
    "\n",
    "#### Maintenance\n",
    "- Schedule regular model retraining\n",
    "- Monitor data drift and model performance\n",
    "- Update dependencies and security patches\n",
    "- Backup models and configuration regularly"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
