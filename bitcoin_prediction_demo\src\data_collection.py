"""
Bitcoin Data Collection Module

This module handles data collection from various sources including:
- Yahoo Finance (yfinance)
- Alternative data sources
- Technical indicators calculation
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import requests
import time
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BitcoinDataCollector:
    """
    Comprehensive Bitcoin data collection class
    """
    
    def __init__(self):
        self.ticker = "BTC-USD"
        self.data_cache = {}
    
    def collect_price_data(self, 
                          period: str = "2y", 
                          interval: str = "1d",
                          use_cache: bool = True) -> Optional[pd.DataFrame]:
        """
        Collect Bitcoin price data from Yahoo Finance
        
        Args:
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
            use_cache: Whether to use cached data
            
        Returns:
            DataFrame with OHLCV data
        """
        cache_key = f"{period}_{interval}"
        
        if use_cache and cache_key in self.data_cache:
            logger.info(f"Using cached data for {cache_key}")
            return self.data_cache[cache_key]
        
        try:
            logger.info(f"Collecting Bitcoin data: period={period}, interval={interval}")
            btc = yf.Ticker(self.ticker)
            data = btc.history(period=period, interval=interval)
            
            if data.empty:
                logger.error("No data retrieved from Yahoo Finance")
                return None
            
            # Basic data cleaning
            data = data.dropna()
            
            # Add basic derived features
            data['Returns'] = data['Close'].pct_change()
            data['Log_Returns'] = np.log(data['Close'] / data['Close'].shift(1))
            data['High_Low_Ratio'] = data['High'] / data['Low']
            data['Volume_Price_Ratio'] = data['Volume'] / data['Close']
            
            # Cache the data
            if use_cache:
                self.data_cache[cache_key] = data.copy()
            
            logger.info(f"Successfully collected {len(data)} records")
            return data
            
        except Exception as e:
            logger.error(f"Error collecting data: {e}")
            return None
    
    def add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add comprehensive technical indicators to the dataset
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical indicators
        """
        df = data.copy()
        
        try:
            # Moving Averages
            for window in [7, 14, 21, 30, 50, 100, 200]:
                df[f'MA_{window}'] = df['Close'].rolling(window=window).mean()
                df[f'EMA_{window}'] = df['Close'].ewm(span=window).mean()
            
            # RSI (Relative Strength Index)
            delta = df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            df['BB_Middle'] = df['Close'].rolling(window=20).mean()
            bb_std = df['Close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
            df['BB_Width'] = df['BB_Upper'] - df['BB_Lower']
            df['BB_Position'] = (df['Close'] - df['BB_Lower']) / df['BB_Width']
            
            # MACD
            exp1 = df['Close'].ewm(span=12).mean()
            exp2 = df['Close'].ewm(span=26).mean()
            df['MACD'] = exp1 - exp2
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # Stochastic Oscillator
            low_14 = df['Low'].rolling(window=14).min()
            high_14 = df['High'].rolling(window=14).max()
            df['Stoch_K'] = 100 * ((df['Close'] - low_14) / (high_14 - low_14))
            df['Stoch_D'] = df['Stoch_K'].rolling(window=3).mean()
            
            # Average True Range (ATR)
            high_low = df['High'] - df['Low']
            high_close = np.abs(df['High'] - df['Close'].shift())
            low_close = np.abs(df['Low'] - df['Close'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(window=14).mean()
            
            # Commodity Channel Index (CCI)
            tp = (df['High'] + df['Low'] + df['Close']) / 3
            sma_tp = tp.rolling(window=20).mean()
            mad = tp.rolling(window=20).apply(lambda x: np.abs(x - x.mean()).mean())
            df['CCI'] = (tp - sma_tp) / (0.015 * mad)
            
            # Williams %R
            df['Williams_R'] = -100 * ((high_14 - df['Close']) / (high_14 - low_14))
            
            # Rate of Change (ROC)
            for period in [1, 5, 10, 20]:
                df[f'ROC_{period}'] = ((df['Close'] - df['Close'].shift(period)) / df['Close'].shift(period)) * 100
            
            # Volatility measures
            for window in [10, 20, 30]:
                df[f'Volatility_{window}'] = df['Returns'].rolling(window=window).std() * np.sqrt(365)
            
            logger.info(f"Added technical indicators. Dataset now has {df.shape[1]} columns")
            return df
            
        except Exception as e:
            logger.error(f"Error adding technical indicators: {e}")
            return data
    
    def get_market_data(self, 
                       period: str = "2y",
                       include_indicators: bool = True) -> Optional[pd.DataFrame]:
        """
        Get comprehensive market data with optional technical indicators
        
        Args:
            period: Data period
            include_indicators: Whether to include technical indicators
            
        Returns:
            Complete dataset ready for modeling
        """
        # Collect base price data
        data = self.collect_price_data(period=period)
        
        if data is None:
            return None
        
        # Add technical indicators if requested
        if include_indicators:
            data = self.add_technical_indicators(data)
        
        # Add time-based features
        data['Day_of_Week'] = data.index.dayofweek
        data['Month'] = data.index.month
        data['Quarter'] = data.index.quarter
        data['Year'] = data.index.year
        data['Is_Weekend'] = data['Day_of_Week'].isin([5, 6]).astype(int)
        
        # Add lag features
        for lag in [1, 2, 3, 5, 7, 14, 21, 30]:
            data[f'Close_Lag_{lag}'] = data['Close'].shift(lag)
            data[f'Returns_Lag_{lag}'] = data['Returns'].shift(lag)
            data[f'Volume_Lag_{lag}'] = data['Volume'].shift(lag)
        
        # Remove rows with NaN values
        data = data.dropna()
        
        logger.info(f"Final dataset shape: {data.shape}")
        return data


def main():
    """
    Example usage of the BitcoinDataCollector
    """
    collector = BitcoinDataCollector()
    
    # Collect comprehensive data
    data = collector.get_market_data(period="1y", include_indicators=True)
    
    if data is not None:
        print(f"Collected {len(data)} records with {data.shape[1]} features")
        print(f"Date range: {data.index.min()} to {data.index.max()}")
        print(f"Current price: ${data['Close'].iloc[-1]:.2f}")
        
        # Save to file
        data.to_csv("../data/processed/bitcoin_data_with_indicators.csv")
        print("Data saved to ../data/processed/bitcoin_data_with_indicators.csv")
    else:
        print("Failed to collect data")


if __name__ == "__main__":
    main()
