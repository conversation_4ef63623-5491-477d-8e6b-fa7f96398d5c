"""
Comprehensive Model Evaluation Module

This module provides evaluation metrics and analysis tools including:
- MAPE, RMSE, MAE, R²
- Directional accuracy
- Risk-adjusted returns (Sharpe ratio)
- Maximum drawdown
- Statistical significance tests
"""

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class ModelEvaluator:
    """
    Comprehensive model evaluation class
    """
    
    def __init__(self):
        self.results = {}
    
    def calculate_basic_metrics(self, actual: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """
        Calculate basic regression metrics
        
        Args:
            actual: Actual values
            predicted: Predicted values
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        # Basic metrics
        metrics['MAE'] = mean_absolute_error(actual, predicted)
        metrics['RMSE'] = np.sqrt(mean_squared_error(actual, predicted))
        metrics['MSE'] = mean_squared_error(actual, predicted)
        metrics['R2'] = r2_score(actual, predicted)
        
        # MAPE (Mean Absolute Percentage Error)
        metrics['MAPE'] = np.mean(np.abs((actual - predicted) / actual)) * 100
        
        # SMAPE (Symmetric Mean Absolute Percentage Error)
        metrics['SMAPE'] = np.mean(2 * np.abs(actual - predicted) / (np.abs(actual) + np.abs(predicted))) * 100
        
        # Mean Error (Bias)
        metrics['ME'] = np.mean(predicted - actual)
        
        # Mean Absolute Scaled Error (MASE) - requires seasonal naive forecast
        naive_forecast = np.roll(actual, 1)[1:]  # Simple lag-1 naive forecast
        actual_subset = actual[1:]
        mae_naive = np.mean(np.abs(actual_subset - naive_forecast))
        if mae_naive != 0:
            metrics['MASE'] = metrics['MAE'] / mae_naive
        else:
            metrics['MASE'] = np.inf
        
        return metrics
    
    def calculate_directional_accuracy(self, actual: np.ndarray, predicted: np.ndarray) -> float:
        """
        Calculate directional accuracy (percentage of correct direction predictions)
        
        Args:
            actual: Actual values
            predicted: Predicted values
            
        Returns:
            Directional accuracy percentage
        """
        if len(actual) < 2:
            return 0.0
        
        actual_direction = np.diff(actual) > 0
        predicted_direction = np.diff(predicted) > 0
        
        correct_directions = np.sum(actual_direction == predicted_direction)
        total_directions = len(actual_direction)
        
        return (correct_directions / total_directions) * 100
    
    def calculate_financial_metrics(self, actual: np.ndarray, predicted: np.ndarray, 
                                  risk_free_rate: float = 0.02) -> Dict[str, float]:
        """
        Calculate financial performance metrics
        
        Args:
            actual: Actual price values
            predicted: Predicted price values
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Dictionary of financial metrics
        """
        metrics = {}
        
        # Calculate returns
        actual_returns = np.diff(actual) / actual[:-1]
        predicted_returns = np.diff(predicted) / predicted[:-1]
        
        # Strategy returns (assuming we trade based on predictions)
        strategy_returns = np.where(predicted_returns > 0, actual_returns, -actual_returns)
        
        # Annualized returns
        trading_days = 252
        metrics['Annual_Return'] = np.mean(strategy_returns) * trading_days * 100
        metrics['Annual_Volatility'] = np.std(strategy_returns) * np.sqrt(trading_days) * 100
        
        # Sharpe Ratio
        excess_returns = strategy_returns - risk_free_rate / trading_days
        if np.std(excess_returns) != 0:
            metrics['Sharpe_Ratio'] = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(trading_days)
        else:
            metrics['Sharpe_Ratio'] = 0
        
        # Maximum Drawdown
        cumulative_returns = np.cumprod(1 + strategy_returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        metrics['Max_Drawdown'] = np.min(drawdown) * 100
        
        # Calmar Ratio (Annual Return / Max Drawdown)
        if metrics['Max_Drawdown'] != 0:
            metrics['Calmar_Ratio'] = metrics['Annual_Return'] / abs(metrics['Max_Drawdown'])
        else:
            metrics['Calmar_Ratio'] = np.inf
        
        # Win Rate
        positive_returns = strategy_returns > 0
        metrics['Win_Rate'] = np.mean(positive_returns) * 100
        
        # Profit Factor
        positive_sum = np.sum(strategy_returns[strategy_returns > 0])
        negative_sum = abs(np.sum(strategy_returns[strategy_returns < 0]))
        if negative_sum != 0:
            metrics['Profit_Factor'] = positive_sum / negative_sum
        else:
            metrics['Profit_Factor'] = np.inf
        
        return metrics
    
    def statistical_significance_tests(self, actual: np.ndarray, predicted: np.ndarray) -> Dict[str, Dict]:
        """
        Perform statistical significance tests
        
        Args:
            actual: Actual values
            predicted: Predicted values
            
        Returns:
            Dictionary of test results
        """
        tests = {}
        
        # Residuals
        residuals = actual - predicted
        
        # Normality test (Shapiro-Wilk)
        if len(residuals) <= 5000:  # Shapiro-Wilk has sample size limitations
            shapiro_stat, shapiro_p = stats.shapiro(residuals)
            tests['Normality_Test'] = {
                'test': 'Shapiro-Wilk',
                'statistic': shapiro_stat,
                'p_value': shapiro_p,
                'is_normal': shapiro_p > 0.05
            }
        else:
            # Use Kolmogorov-Smirnov test for larger samples
            ks_stat, ks_p = stats.kstest(residuals, 'norm')
            tests['Normality_Test'] = {
                'test': 'Kolmogorov-Smirnov',
                'statistic': ks_stat,
                'p_value': ks_p,
                'is_normal': ks_p > 0.05
            }
        
        # Autocorrelation test (Ljung-Box)
        from statsmodels.stats.diagnostic import acorr_ljungbox
        lb_result = acorr_ljungbox(residuals, lags=10, return_df=True)
        tests['Autocorrelation_Test'] = {
            'test': 'Ljung-Box',
            'p_values': lb_result['lb_pvalue'].values,
            'has_autocorrelation': any(lb_result['lb_pvalue'] < 0.05)
        }
        
        # Heteroscedasticity test (Breusch-Pagan)
        try:
            from statsmodels.stats.diagnostic import het_breuschpagan
            from statsmodels.regression.linear_model import OLS
            from statsmodels.tools import add_constant
            
            # Simple regression for heteroscedasticity test
            X = add_constant(predicted)
            bp_stat, bp_p, _, _ = het_breuschpagan(residuals, X)
            tests['Heteroscedasticity_Test'] = {
                'test': 'Breusch-Pagan',
                'statistic': bp_stat,
                'p_value': bp_p,
                'is_homoscedastic': bp_p > 0.05
            }
        except:
            tests['Heteroscedasticity_Test'] = {'error': 'Could not perform test'}
        
        return tests
    
    def evaluate_model(self, actual: np.ndarray, predicted: np.ndarray, 
                      model_name: str, risk_free_rate: float = 0.02) -> Dict[str, any]:
        """
        Comprehensive model evaluation
        
        Args:
            actual: Actual values
            predicted: Predicted values
            model_name: Name of the model
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Complete evaluation results
        """
        results = {
            'model_name': model_name,
            'basic_metrics': self.calculate_basic_metrics(actual, predicted),
            'directional_accuracy': self.calculate_directional_accuracy(actual, predicted),
            'financial_metrics': self.calculate_financial_metrics(actual, predicted, risk_free_rate),
            'statistical_tests': self.statistical_significance_tests(actual, predicted)
        }
        
        # Store results
        self.results[model_name] = results
        
        return results
    
    def compare_models(self, results_dict: Dict[str, Dict]) -> pd.DataFrame:
        """
        Compare multiple models
        
        Args:
            results_dict: Dictionary of model results
            
        Returns:
            Comparison DataFrame
        """
        comparison_data = []
        
        for model_name, results in results_dict.items():
            row = {'Model': model_name}
            
            # Basic metrics
            row.update(results.get('basic_metrics', {}))
            
            # Directional accuracy
            row['Directional_Accuracy'] = results.get('directional_accuracy', 0)
            
            # Financial metrics
            row.update(results.get('financial_metrics', {}))
            
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def create_evaluation_report(self, model_name: str, save_path: Optional[str] = None) -> str:
        """
        Create detailed evaluation report
        
        Args:
            model_name: Name of the model to report on
            save_path: Path to save the report
            
        Returns:
            Report as string
        """
        if model_name not in self.results:
            return f"No results found for model: {model_name}"
        
        results = self.results[model_name]
        
        report = f"""
# Model Evaluation Report: {model_name}

## Basic Performance Metrics
"""
        
        basic_metrics = results['basic_metrics']
        for metric, value in basic_metrics.items():
            report += f"- **{metric}**: {value:.4f}\n"
        
        report += f"""
## Directional Accuracy
- **Directional Accuracy**: {results['directional_accuracy']:.2f}%

## Financial Performance Metrics
"""
        
        financial_metrics = results['financial_metrics']
        for metric, value in financial_metrics.items():
            report += f"- **{metric}**: {value:.4f}\n"
        
        report += f"""
## Statistical Tests
"""
        
        stat_tests = results['statistical_tests']
        
        # Normality test
        norm_test = stat_tests.get('Normality_Test', {})
        report += f"- **Normality Test ({norm_test.get('test', 'N/A')})**: "
        report += f"p-value = {norm_test.get('p_value', 'N/A'):.4f}, "
        report += f"Normal: {norm_test.get('is_normal', 'N/A')}\n"
        
        # Autocorrelation test
        autocorr_test = stat_tests.get('Autocorrelation_Test', {})
        report += f"- **Autocorrelation Test**: "
        report += f"Has autocorrelation: {autocorr_test.get('has_autocorrelation', 'N/A')}\n"
        
        # Heteroscedasticity test
        hetero_test = stat_tests.get('Heteroscedasticity_Test', {})
        if 'error' not in hetero_test:
            report += f"- **Heteroscedasticity Test**: "
            report += f"p-value = {hetero_test.get('p_value', 'N/A'):.4f}, "
            report += f"Homoscedastic: {hetero_test.get('is_homoscedastic', 'N/A')}\n"
        
        if save_path:
            with open(save_path, 'w') as f:
                f.write(report)
            print(f"Report saved to {save_path}")
        
        return report


def model_selection_criteria(results_dict: Dict[str, Dict], 
                           weights: Dict[str, float] = None) -> Dict[str, float]:
    """
    Calculate composite model selection score
    
    Args:
        results_dict: Dictionary of model results
        weights: Weights for different metrics
        
    Returns:
        Dictionary of composite scores
    """
    if weights is None:
        weights = {
            'MAPE': -0.3,  # Lower is better
            'Directional_Accuracy': 0.2,  # Higher is better
            'Sharpe_Ratio': 0.3,  # Higher is better
            'Max_Drawdown': -0.2  # Lower absolute value is better
        }
    
    scores = {}
    
    for model_name, results in results_dict.items():
        score = 0
        
        # MAPE (normalize and invert since lower is better)
        mape = results.get('basic_metrics', {}).get('MAPE', 100)
        score += weights['MAPE'] * (mape / 100)
        
        # Directional Accuracy
        dir_acc = results.get('directional_accuracy', 50)
        score += weights['Directional_Accuracy'] * (dir_acc / 100)
        
        # Sharpe Ratio
        sharpe = results.get('financial_metrics', {}).get('Sharpe_Ratio', 0)
        score += weights['Sharpe_Ratio'] * max(0, min(sharpe, 3))  # Cap at 3
        
        # Max Drawdown (invert since lower absolute value is better)
        max_dd = results.get('financial_metrics', {}).get('Max_Drawdown', -50)
        score += weights['Max_Drawdown'] * (abs(max_dd) / 100)
        
        scores[model_name] = score
    
    return scores


if __name__ == "__main__":
    # Example usage
    evaluator = ModelEvaluator()
    
    # Generate sample data
    np.random.seed(42)
    actual = np.random.randn(100).cumsum() + 50000
    predicted = actual + np.random.randn(100) * 1000
    
    # Evaluate model
    results = evaluator.evaluate_model(actual, predicted, "Sample Model")
    
    # Print report
    report = evaluator.create_evaluation_report("Sample Model")
    print(report)
