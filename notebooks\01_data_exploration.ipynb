{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bitcoin Price Data Exploration\n", "\n", "This notebook provides an initial exploration of the collected Bitcoin price data.\n", "\n", "## Objectives\n", "1. Load and examine the raw Bitcoin price data\n", "2. Perform basic statistical analysis\n", "3. Visualize price trends and patterns\n", "4. Identify data quality issues\n", "5. Understand volatility characteristics"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure pandas display\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data from: ..\\data\\raw\\BTC_USD_yahoo_20250706_150051.csv\n", "Data shape: (2014, 8)\n", "Date range: 2020-01-01 00:00:00+00:00 to 2025-07-06 00:00:00+00:00\n"]}], "source": ["# Load the most recent Bitcoin data file\n", "data_dir = Path('../data/raw')\n", "data_files = list(data_dir.glob('BTC_USD_*.csv'))\n", "\n", "if not data_files:\n", "    print(\"No Bitcoin data files found. Please run data collection first:\")\n", "    print(\"python src/data_collection/collect_data.py\")\n", "else:\n", "    # Load the most recent file\n", "    latest_file = max(data_files, key=lambda x: x.stat().st_mtime)\n", "    print(f\"Loading data from: {latest_file}\")\n", "    \n", "    df = pd.read_csv(latest_file, index_col=0, parse_dates=True)\n", "    print(f\"Data shape: {df.shape}\")\n", "    print(f\"Date range: {df.index.min()} to {df.index.max()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Data Examination"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 2014 entries, 2020-01-01 00:00:00+00:00 to 2025-07-06 00:00:00+00:00\n", "Data columns (total 8 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   open          2014 non-null   float64\n", " 1   high          2014 non-null   float64\n", " 2   low           2014 non-null   float64\n", " 3   close         2014 non-null   float64\n", " 4   adj close     2014 non-null   float64\n", " 5   volume        2014 non-null   int64  \n", " 6   dividends     2014 non-null   float64\n", " 7   stock splits  2014 non-null   float64\n", "dtypes: float64(7), int64(1)\n", "memory usage: 141.6 KB\n", "None\n", "\n", "First 5 rows:\n", "                                  open         high          low        close  \\\n", "Date                                                                            \n", "2020-01-01 00:00:00+00:00  7194.892090  7254.330566  7174.944336  7200.174316   \n", "2020-01-02 00:00:00+00:00  7202.551270  7212.155273  6935.270020  6985.470215   \n", "2020-01-03 00:00:00+00:00  6984.428711  7413.715332  6914.996094  7344.884277   \n", "2020-01-04 00:00:00+00:00  7345.375488  7427.385742  7309.514160  7410.656738   \n", "2020-01-05 00:00:00+00:00  7410.451660  7544.497070  7400.535645  7411.317383   \n", "\n", "                             adj close       volume  dividends  stock splits  \n", "Date                                                                          \n", "2020-01-01 00:00:00+00:00  7200.174316  18565664997        0.0           0.0  \n", "2020-01-02 00:00:00+00:00  6985.470215  20802083465        0.0           0.0  \n", "2020-01-03 00:00:00+00:00  7344.884277  28111481032        0.0           0.0  \n", "2020-01-04 00:00:00+00:00  7410.656738  18444271275        0.0           0.0  \n", "2020-01-05 00:00:00+00:00  7411.317383  19725074095        0.0           0.0  \n", "\n", "Last 5 rows:\n", "                                    open           high            low  \\\n", "Date                                                                     \n", "2025-07-02 00:00:00+00:00  105703.101562  109763.656250  105157.398438   \n", "2025-07-03 00:00:00+00:00  108845.015625  110541.460938  108605.796875   \n", "2025-07-04 00:00:00+00:00  109635.656250  109751.984375  107296.382812   \n", "2025-07-05 00:00:00+00:00  108015.835938  108381.343750  107842.273438   \n", "2025-07-06 00:00:00+00:00  108232.257812  108284.156250  108005.718750   \n", "\n", "                                   close      adj close       volume  \\\n", "Date                                                                   \n", "2025-07-02 00:00:00+00:00  108859.320312  108859.320312  56248657737   \n", "2025-07-03 00:00:00+00:00  109647.976562  109647.976562  50494742270   \n", "2025-07-04 00:00:00+00:00  108034.335938  108034.335938  42616442656   \n", "2025-07-05 00:00:00+00:00  108231.179688  108231.179688  30615537520   \n", "2025-07-06 00:00:00+00:00  108048.718750  108048.718750  29990070272   \n", "\n", "                           dividends  stock splits  \n", "Date                                                \n", "2025-07-02 00:00:00+00:00        0.0           0.0  \n", "2025-07-03 00:00:00+00:00        0.0           0.0  \n", "2025-07-04 00:00:00+00:00        0.0           0.0  \n", "2025-07-05 00:00:00+00:00        0.0           0.0  \n", "2025-07-06 00:00:00+00:00        0.0           0.0  \n"]}], "source": ["# Display basic information about the dataset\n", "print(\"Dataset Info:\")\n", "print(df.info())\n", "print(\"\\nFirst 5 rows:\")\n", "print(df.head())\n", "print(\"\\nLast 5 rows:\")\n", "print(df.tail())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values:\n", "open            0\n", "high            0\n", "low             0\n", "close           0\n", "adj close       0\n", "volume          0\n", "dividends       0\n", "stock splits    0\n", "dtype: int64\n", "\n", "Total missing values: 0\n", "Percentage of missing values: 0.00%\n"]}], "source": ["# Check for missing values\n", "print(\"Missing values:\")\n", "print(df.isnull().sum())\n", "print(f\"\\nTotal missing values: {df.isnull().sum().sum()}\")\n", "print(f\"Percentage of missing values: {(df.isnull().sum().sum() / df.size) * 100:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Statistical Analysis"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Descriptive Statistics:\n", "                open           high            low          close  \\\n", "count    2014.000000    2014.000000    2014.000000    2014.000000   \n", "mean    41852.151694   42727.563572   40939.779973   41900.090232   \n", "std     26748.136794   27236.218260   26238.681257   26776.650242   \n", "min      5002.578125    5331.833984    4106.980957    4970.788086   \n", "25%     20629.445801   21144.436035   20235.452637   20648.897949   \n", "50%     36827.761719   37788.560547   35513.802734   36873.263672   \n", "75%     59121.095703   60654.547852   57850.499023   59122.444336   \n", "max    111679.359375  111970.171875  109285.070312  111673.281250   \n", "\n", "           adj close        volume  dividends  stock splits  \n", "count    2014.000000  2.014000e+03     2014.0        2014.0  \n", "mean    41900.090232  3.417517e+10        0.0           0.0  \n", "std     26776.650242  1.979844e+10        0.0           0.0  \n", "min      4970.788086  5.331173e+09        0.0           0.0  \n", "25%     20648.897949  2.111849e+10        0.0           0.0  \n", "50%     36873.263672  3.061990e+10        0.0           0.0  \n", "75%     59122.444336  4.198601e+10        0.0           0.0  \n", "max    111673.281250  3.509679e+11        0.0           0.0  \n"]}], "source": ["# Descriptive statistics\n", "print(\"Descriptive Statistics:\")\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Returns Statistics:\n", "           returns  log_returns\n", "count  2013.000000  2013.000000\n", "mean      0.001894     0.001345\n", "std       0.032803     0.033290\n", "min      -0.371695    -0.464730\n", "25%      -0.012754    -0.012836\n", "50%       0.000660     0.000659\n", "75%       0.016031     0.015904\n", "max       0.187465     0.171821\n"]}], "source": ["# Calculate daily returns\n", "df['returns'] = df['close'].pct_change()\n", "df['log_returns'] = np.log(df['close'] / df['close'].shift(1))\n", "\n", "print(\"Returns Statistics:\")\n", "print(df[['returns', 'log_returns']].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Price Visualization"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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**************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create comprehensive price visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Price over time\n", "axes[0, 0].plot(df.index, df['close'], linewidth=1)\n", "axes[0, 0].set_title('Bitcoin Price Over Time')\n", "axes[0, 0].set_ylabel('Price (USD)')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Volume over time\n", "axes[0, 1].plot(df.index, df['volume'], color='orange', linewidth=1)\n", "axes[0, 1].set_title('Trading Volume Over Time')\n", "axes[0, 1].set_ylabel('Volume')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Daily returns\n", "axes[1, 0].plot(df.index, df['returns'], color='green', linewidth=0.5)\n", "axes[1, 0].set_title('Daily Returns')\n", "axes[1, 0].set_ylabel('Returns')\n", "axes[1, 0].axhline(y=0, color='red', linestyle='--', alpha=0.5)\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Returns distribution\n", "axes[1, 1].hist(df['returns'].dropna(), bins=50, alpha=0.7, color='purple')\n", "axes[1, 1].set_title('Distribution of Daily Returns')\n", "axes[1, 1].set_xlabel('Returns')\n", "axes[1, 1].set_ylabel('Frequency')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Volatility Analysis"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Average volatility: 58.29%\n", "Maximum volatility: 174.49%\n", "Minimum volatility: 16.98%\n"]}], "source": ["# Calculate rolling volatility\n", "df['volatility_30d'] = df['returns'].rolling(window=30).std() * np.sqrt(365)\n", "\n", "# Plot volatility\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(df.index, df['volatility_30d'], linewidth=1, color='red')\n", "plt.title('30-Day Rolling Volatility (Annualized)')\n", "plt.ylabel('Volatility')\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(f\"Average volatility: {df['volatility_30d'].mean():.2%}\")\n", "print(f\"Maximum volatility: {df['volatility_30d'].max():.2%}\")\n", "print(f\"Minimum volatility: {df['volatility_30d'].min():.2%}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data Quality Checks:\n", "1. Duplicate dates: 0\n", "2. Zero prices: 0\n", "3. Negative prices: 0\n", "4. Zero volume: 0\n", "5. Extreme price movements (>50%): 0\n"]}], "source": ["# Check for data quality issues\n", "print(\"Data Quality Checks:\")\n", "print(f\"1. Duplicate dates: {df.index.duplicated().sum()}\")\n", "print(f\"2. Zero prices: {(df['close'] == 0).sum()}\")\n", "print(f\"3. Negative prices: {(df['close'] < 0).sum()}\")\n", "print(f\"4. Zero volume: {(df['volume'] == 0).sum()}\")\n", "\n", "# Check for extreme price movements (>50% in a day)\n", "extreme_moves = df[abs(df['returns']) > 0.5]\n", "print(f\"5. Extreme price movements (>50%): {len(extreme_moves)}\")\n", "if len(extreme_moves) > 0:\n", "    print(\"Extreme movement dates:\")\n", "    print(extreme_moves[['close', 'returns']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> and Next Steps"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "DATA EXPLORATION SUMMARY\n", "==================================================\n", "Dataset period: 2020-01-01 to 2025-07-06\n", "Total observations: 2,014\n", "Data completeness: 99.9%\n", "\n", "Price range: $4,970.79 - $111,673.28\n", "Average daily return: 0.19%\n", "Daily return volatility: 3.28%\n", "Annualized volatility: 62.67%\n", "\n", "Next steps:\n", "1. Feature engineering (technical indicators, lags)\n", "2. Data preprocessing and cleaning\n", "3. Model development and training\n", "4. Model evaluation and comparison\n"]}], "source": ["# Summary statistics\n", "print(\"=\" * 50)\n", "print(\"DATA EXPLORATION SUMMARY\")\n", "print(\"=\" * 50)\n", "print(f\"Dataset period: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}\")\n", "print(f\"Total observations: {len(df):,}\")\n", "print(f\"Data completeness: {((df.size - df.isnull().sum().sum()) / df.size) * 100:.1f}%\")\n", "print(f\"\\nPrice range: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}\")\n", "print(f\"Average daily return: {df['returns'].mean():.2%}\")\n", "print(f\"Daily return volatility: {df['returns'].std():.2%}\")\n", "print(f\"Annualized volatility: {df['returns'].std() * np.sqrt(365):.2%}\")\n", "\n", "print(\"\\nNext steps:\")\n", "print(\"1. Feature engineering (technical indicators, lags)\")\n", "print(\"2. Data preprocessing and cleaning\")\n", "print(\"3. Model development and training\")\n", "print(\"4. Model evaluation and comparison\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}