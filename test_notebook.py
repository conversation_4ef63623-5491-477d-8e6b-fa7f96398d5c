#!/usr/bin/env python3
"""
Test script to verify the Bitcoin Price Prediction notebook works correctly
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """Test all required imports"""
    print("🧪 Testing imports...")
    
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        import yfinance as yf
        import warnings
        from datetime import datetime, timedelta
        import joblib
        import json
        from pathlib import Path
        
        # ML libraries
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.preprocessing import StandardScaler
        from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
        import xgboost as xgb
        import lightgbm as lgb
        
        # Time series
        from statsmodels.tsa.arima.model import ARIMA
        
        print("✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_data_fetching():
    """Test Bitcoin data fetching"""
    print("\n🧪 Testing data fetching...")
    
    try:
        import yfinance as yf
        
        # Fetch small amount of data
        btc = yf.download('BTC-USD', period='5d', interval='1d')
        
        if len(btc) > 0:
            print(f"✅ Data fetching successful! Got {len(btc)} days of data")
            return True
        else:
            print("❌ No data returned")
            return False
            
    except Exception as e:
        print(f"❌ Data fetching failed: {e}")
        return False

def test_model_files():
    """Test if production model files exist"""
    print("\n🧪 Testing model files...")
    
    models_dir = Path('models')
    required_models = [
        'arima_price_model.joblib',
        'arima_returns_model.joblib',
        'randomforest_target_return_1d_model.joblib',
        'xgboost_target_return_1d_model.joblib',
        'lightgbm_target_return_1d_model.joblib',
        'scaler_standard.joblib'
    ]
    
    missing_models = []
    for model_file in required_models:
        if not (models_dir / model_file).exists():
            missing_models.append(model_file)
    
    if not missing_models:
        print("✅ All required model files found!")
        return True
    else:
        print(f"⚠️ Missing model files: {missing_models}")
        print("   Run the training scripts first to generate these models")
        return False

def test_production_files():
    """Test if production system files exist"""
    print("\n🧪 Testing production files...")
    
    production_files = [
        'bitcoin_prediction_demo/src/production_model_manager.py',
        'bitcoin_prediction_demo/src/production_data_pipeline.py',
        'bitcoin_prediction_demo/src/production_prediction_api.py'
    ]
    
    missing_files = []
    for file_path in production_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ All production files found!")
        return True
    else:
        print(f"⚠️ Missing production files: {missing_files}")
        return False

def test_notebook_exists():
    """Test if the main notebook exists"""
    print("\n🧪 Testing notebook file...")
    
    notebook_path = Path('Bitcoin_Price_Prediction_Complete.ipynb')
    
    if notebook_path.exists():
        print("✅ Main notebook found!")
        
        # Check file size
        size_mb = notebook_path.stat().st_size / (1024 * 1024)
        print(f"   File size: {size_mb:.2f} MB")
        
        return True
    else:
        print("❌ Main notebook not found!")
        return False

def main():
    """Run all tests"""
    print("🚀 Bitcoin Price Prediction System - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Data Fetching Test", test_data_fetching),
        ("Model Files Test", test_model_files),
        ("Production Files Test", test_production_files),
        ("Notebook File Test", test_notebook_exists)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<25} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"Overall: {passed}/{len(results)} tests passed ({passed/len(results)*100:.0f}%)")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The system is ready to use.")
        return 0
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed. Check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
