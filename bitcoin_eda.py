"""Comprehensive Exploratory Data Analysis for Bitcoin Price Data."""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

def load_bitcoin_data():
    """Load the most recent Bitcoin data file."""
    data_dir = Path('data/raw')
    data_files = list(data_dir.glob('BTC_USD_*.csv'))
    
    if not data_files:
        raise FileNotFoundError("No Bitcoin data files found. Run data collection first.")
    
    # Load the most recent file
    latest_file = max(data_files, key=lambda x: x.stat().st_mtime)
    print(f"Loading data from: {latest_file}")
    
    df = pd.read_csv(latest_file, index_col=0, parse_dates=True)
    print(f"Data shape: {df.shape}")
    print(f"Date range: {df.index.min()} to {df.index.max()}")
    
    return df

def basic_data_analysis(df):
    """Perform basic data analysis."""
    print("\n" + "="*50)
    print("BASIC DATA ANALYSIS")
    print("="*50)
    
    print("\nDataset Info:")
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Data types:\n{df.dtypes}")
    
    print("\nMissing Values:")
    missing = df.isnull().sum()
    print(missing)
    print(f"Total missing: {missing.sum()}")
    
    print("\nDescriptive Statistics:")
    print(df.describe())
    
    return df

def calculate_returns_and_volatility(df):
    """Calculate returns and volatility metrics."""
    print("\n" + "="*50)
    print("RETURNS AND VOLATILITY ANALYSIS")
    print("="*50)
    
    # Calculate returns
    df['daily_return'] = df['close'].pct_change()
    df['log_return'] = np.log(df['close'] / df['close'].shift(1))
    
    # Calculate volatility measures
    df['volatility_7d'] = df['daily_return'].rolling(window=7).std() * np.sqrt(365)
    df['volatility_30d'] = df['daily_return'].rolling(window=30).std() * np.sqrt(365)
    df['volatility_90d'] = df['daily_return'].rolling(window=90).std() * np.sqrt(365)
    
    print("Returns Statistics:")
    returns_stats = df[['daily_return', 'log_return']].describe()
    print(returns_stats)
    
    print(f"\nAnnualized Volatility: {df['daily_return'].std() * np.sqrt(365):.2%}")
    print(f"Sharpe Ratio (assuming 2% risk-free rate): {(df['daily_return'].mean() * 365 - 0.02) / (df['daily_return'].std() * np.sqrt(365)):.2f}")
    
    return df

def create_price_visualizations(df):
    """Create comprehensive price visualizations."""
    print("\n" + "="*50)
    print("CREATING PRICE VISUALIZATIONS")
    print("="*50)
    
    # Create output directory
    Path('results').mkdir(exist_ok=True)
    
    # Figure 1: Price and Volume Overview
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Price over time
    axes[0, 0].plot(df.index, df['close'], linewidth=1, color='blue')
    axes[0, 0].set_title('Bitcoin Price Over Time', fontsize=14, fontweight='bold')
    axes[0, 0].set_ylabel('Price (USD)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Volume over time
    axes[0, 1].plot(df.index, df['volume'], color='orange', linewidth=1)
    axes[0, 1].set_title('Trading Volume Over Time', fontsize=14, fontweight='bold')
    axes[0, 1].set_ylabel('Volume')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # Daily returns
    axes[1, 0].plot(df.index, df['daily_return'], color='green', linewidth=0.5, alpha=0.7)
    axes[1, 0].set_title('Daily Returns', fontsize=14, fontweight='bold')
    axes[1, 0].set_ylabel('Returns')
    axes[1, 0].axhline(y=0, color='red', linestyle='--', alpha=0.5)
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].tick_params(axis='x', rotation=45)
    
    # Returns distribution
    axes[1, 1].hist(df['daily_return'].dropna(), bins=50, alpha=0.7, color='purple', density=True)
    axes[1, 1].set_title('Distribution of Daily Returns', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('Returns')
    axes[1, 1].set_ylabel('Density')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/bitcoin_price_overview.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Figure 2: Volatility Analysis
    plt.figure(figsize=(14, 8))
    plt.plot(df.index, df['volatility_30d'], linewidth=1, color='red', label='30-day Volatility')
    plt.plot(df.index, df['volatility_90d'], linewidth=1, color='blue', label='90-day Volatility')
    plt.title('Bitcoin Volatility Over Time (Annualized)', fontsize=16, fontweight='bold')
    plt.ylabel('Volatility')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('results/bitcoin_volatility.png', dpi=300, bbox_inches='tight')
    plt.close()

def analyze_price_patterns(df):
    """Analyze price patterns and seasonality."""
    print("\n" + "="*50)
    print("PRICE PATTERNS AND SEASONALITY")
    print("="*50)
    
    # Add time-based features
    df['year'] = df.index.year
    df['month'] = df.index.month
    df['day_of_week'] = df.index.dayofweek
    df['day_of_year'] = df.index.dayofyear
    
    # Monthly returns analysis
    monthly_returns = df.groupby('month')['daily_return'].agg(['mean', 'std', 'count'])
    print("Monthly Returns Analysis:")
    print(monthly_returns)
    
    # Day of week analysis
    dow_returns = df.groupby('day_of_week')['daily_return'].agg(['mean', 'std', 'count'])
    dow_returns.index = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    print("\nDay of Week Returns Analysis:")
    print(dow_returns)
    
    # Yearly performance
    yearly_performance = df.groupby('year').agg({
        'close': ['first', 'last'],
        'daily_return': ['mean', 'std'],
        'volume': 'mean'
    }).round(4)
    yearly_performance.columns = ['Start_Price', 'End_Price', 'Avg_Return', 'Volatility', 'Avg_Volume']
    yearly_performance['Annual_Return'] = (yearly_performance['End_Price'] / yearly_performance['Start_Price'] - 1)
    print("\nYearly Performance:")
    print(yearly_performance)
    
    return df

def risk_analysis(df):
    """Perform risk analysis."""
    print("\n" + "="*50)
    print("RISK ANALYSIS")
    print("="*50)
    
    returns = df['daily_return'].dropna()
    
    # Value at Risk (VaR)
    var_95 = np.percentile(returns, 5)
    var_99 = np.percentile(returns, 1)
    
    # Expected Shortfall (Conditional VaR)
    es_95 = returns[returns <= var_95].mean()
    es_99 = returns[returns <= var_99].mean()
    
    # Maximum drawdown
    cumulative_returns = (1 + returns).cumprod()
    rolling_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    print(f"Value at Risk (95%): {var_95:.2%}")
    print(f"Value at Risk (99%): {var_99:.2%}")
    print(f"Expected Shortfall (95%): {es_95:.2%}")
    print(f"Expected Shortfall (99%): {es_99:.2%}")
    print(f"Maximum Drawdown: {max_drawdown:.2%}")
    
    # Extreme movements
    extreme_up = returns[returns > 0.1].count()
    extreme_down = returns[returns < -0.1].count()
    print(f"\nDays with >10% gains: {extreme_up}")
    print(f"Days with >10% losses: {extreme_down}")
    
    return df

def main():
    """Main EDA function."""
    print("Bitcoin Price Data - Exploratory Data Analysis")
    print("=" * 60)
    
    try:
        # Load data
        df = load_bitcoin_data()
        
        # Basic analysis
        df = basic_data_analysis(df)
        
        # Returns and volatility
        df = calculate_returns_and_volatility(df)
        
        # Visualizations
        create_price_visualizations(df)
        
        # Pattern analysis
        df = analyze_price_patterns(df)
        
        # Risk analysis
        df = risk_analysis(df)
        
        # Save processed data
        df.to_csv('data/processed/bitcoin_with_features.csv')
        print(f"\n✓ Processed data saved to: data/processed/bitcoin_with_features.csv")
        
        print("\n" + "="*60)
        print("EDA COMPLETED SUCCESSFULLY!")
        print("Check the 'results' folder for visualizations.")
        print("="*60)
        
    except Exception as e:
        print(f"Error during EDA: {e}")

if __name__ == "__main__":
    main()
