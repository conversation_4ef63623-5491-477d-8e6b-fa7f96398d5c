#!/usr/bin/env python3
"""
Bitcoin Price Prediction Demo Launcher

This script provides a simple interface to run the Bitcoin prediction demo
with various options and configurations.
"""

import os
import sys
import argparse
import subprocess
import webbrowser
from pathlib import Path

def setup_environment():
    """
    Set up the Python environment and install required packages
    """
    print("🔧 Setting up environment...")
    
    # Check if requirements.txt exists
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found!")
        return False
    
    try:
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Environment setup completed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def launch_jupyter():
    """
    Launch Jupyter Lab with the demo notebook
    """
    print("🚀 Launching Jupyter Lab...")
    
    try:
        # Change to notebooks directory
        os.chdir("notebooks")
        
        # Launch Jupyter Lab
        subprocess.Popen([sys.executable, "-m", "jupyter", "lab", "06_demo_presentation.ipynb"])
        
        print("✅ Jupyter Lab launched!")
        print("📝 Open the demo notebook: 06_demo_presentation.ipynb")
        
        # Try to open browser automatically
        try:
            webbrowser.open("http://localhost:8888/lab")
        except:
            pass
            
    except Exception as e:
        print(f"❌ Error launching Jupyter Lab: {e}")
        print("💡 Try running manually: jupyter lab notebooks/06_demo_presentation.ipynb")

def run_data_collection():
    """
    Run data collection script
    """
    print("📊 Running data collection...")
    
    try:
        sys.path.append("src")
        from data_collection import BitcoinDataCollector
        
        collector = BitcoinDataCollector()
        data = collector.get_market_data(period="1y", include_indicators=True)
        
        if data is not None:
            # Save data
            os.makedirs("data/processed", exist_ok=True)
            data.to_csv("data/processed/bitcoin_data_demo.csv")
            
            print(f"✅ Data collection completed!")
            print(f"📈 Collected {len(data)} records with {data.shape[1]} features")
            print(f"💾 Data saved to: data/processed/bitcoin_data_demo.csv")
        else:
            print("❌ Data collection failed!")
            
    except Exception as e:
        print(f"❌ Error in data collection: {e}")

def run_model_evaluation():
    """
    Run model evaluation
    """
    print("🤖 Running model evaluation...")
    
    try:
        sys.path.append("src")
        from data_collection import BitcoinDataCollector
        from models.ml_models import evaluate_ml_models
        
        # Collect data
        collector = BitcoinDataCollector()
        data = collector.get_market_data(period="1y", include_indicators=True)
        
        if data is not None:
            # Evaluate models
            results, y_test = evaluate_ml_models(data)
            
            print("✅ Model evaluation completed!")
            print("\n📊 Results Summary:")
            print("=" * 50)
            
            for model_name, metrics in results.items():
                print(f"\n{model_name}:")
                print(f"  MAPE: {metrics['MAPE']:.2f}%")
                print(f"  RMSE: {metrics['RMSE']:.2f}")
                print(f"  Directional Accuracy: {metrics['Directional_Accuracy']:.2f}%")
        else:
            print("❌ Data collection failed!")
            
    except Exception as e:
        print(f"❌ Error in model evaluation: {e}")

def create_sample_data():
    """
    Create sample data for testing
    """
    print("📝 Creating sample data...")
    
    try:
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Create sample Bitcoin data
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        np.random.seed(42)
        
        # Generate realistic Bitcoin price data
        returns = np.random.normal(0.001, 0.04, len(dates))  # Daily returns
        prices = [50000]  # Starting price
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Create DataFrame
        data = pd.DataFrame({
            'Date': dates,
            'Close': prices,
            'Volume': np.random.lognormal(15, 1, len(dates)),
            'High': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
            'Open': [p * (1 + np.random.normal(0, 0.01)) for p in prices]
        })
        
        data.set_index('Date', inplace=True)
        
        # Save sample data
        os.makedirs("data/raw", exist_ok=True)
        data.to_csv("data/raw/sample_bitcoin_data.csv")
        
        print("✅ Sample data created!")
        print(f"📊 Created {len(data)} days of sample data")
        print(f"💾 Saved to: data/raw/sample_bitcoin_data.csv")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")

def show_project_info():
    """
    Display project information
    """
    print("""
🚀 Bitcoin Price Prediction Demo
================================

This project demonstrates comprehensive Bitcoin price prediction using:
• Traditional statistical models (ARIMA)
• Machine learning models (Random Forest, XGBoost, LightGBM)
• Technical indicators and feature engineering
• Interactive visualizations and analysis
• Professional evaluation metrics

📁 Project Structure:
├── notebooks/           # Jupyter notebooks
├── src/                # Source code modules
├── data/               # Data storage
├── models/             # Trained models
├── results/            # Results and figures
├── docs/               # Documentation
└── utils/              # Utility functions

🎯 Key Features:
• Live data collection from Yahoo Finance
• 50+ technical indicators
• Multiple model comparison
• Risk-adjusted performance metrics
• Interactive Plotly visualizations
• Comprehensive lessons learned

📚 Documentation:
• README.md - Project overview
• docs/lessons_learned.md - Detailed insights
• notebooks/06_demo_presentation.ipynb - Main demo

🚀 Quick Start:
1. python run_demo.py --setup     # Install dependencies
2. python run_demo.py --jupyter   # Launch demo notebook
3. python run_demo.py --collect   # Collect fresh data
4. python run_demo.py --evaluate  # Run model evaluation
""")

def main():
    """
    Main function to handle command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Bitcoin Price Prediction Demo Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--setup", action="store_true", 
                       help="Set up environment and install dependencies")
    parser.add_argument("--jupyter", action="store_true", 
                       help="Launch Jupyter Lab with demo notebook")
    parser.add_argument("--collect", action="store_true", 
                       help="Run data collection")
    parser.add_argument("--evaluate", action="store_true", 
                       help="Run model evaluation")
    parser.add_argument("--sample", action="store_true", 
                       help="Create sample data for testing")
    parser.add_argument("--info", action="store_true", 
                       help="Show project information")
    
    args = parser.parse_args()
    
    # Show info if no arguments provided
    if not any(vars(args).values()):
        show_project_info()
        return
    
    # Execute requested actions
    if args.info:
        show_project_info()
    
    if args.setup:
        setup_environment()
    
    if args.sample:
        create_sample_data()
    
    if args.collect:
        run_data_collection()
    
    if args.evaluate:
        run_model_evaluation()
    
    if args.jupyter:
        launch_jupyter()

if __name__ == "__main__":
    main()
