"""
Traditional Statistical Models for Bitcoin Price Prediction

This module implements traditional time series models including:
- ARIMA (AutoRegressive Integrated Moving Average)
- GARCH (Generalized Autoregressive Conditional Heteroskedasticity)
- Exponential Smoothing
"""

import pandas as pd
import numpy as np
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.stattools import adfuller
from statsmodels.stats.diagnostic import acorr_ljungbox
from pmdarima import auto_arima
import warnings
warnings.filterwarnings('ignore')


class ARIMAPredictor:
    """
    ARIMA model for Bitcoin price prediction
    """
    
    def __init__(self, order=None, seasonal_order=None):
        self.order = order
        self.seasonal_order = seasonal_order
        self.model = None
        self.fitted_model = None
        self.is_fitted = False
    
    def check_stationarity(self, series, alpha=0.05):
        """
        Check if time series is stationary using Augmented Dickey-Fuller test
        
        Args:
            series: Time series data
            alpha: Significance level
            
        Returns:
            bool: True if stationary, False otherwise
        """
        result = adfuller(series.dropna())
        p_value = result[1]
        
        return p_value < alpha
    
    def make_stationary(self, series, max_diff=2):
        """
        Make time series stationary through differencing
        
        Args:
            series: Time series data
            max_diff: Maximum number of differences to apply
            
        Returns:
            Tuple of (stationary_series, num_differences)
        """
        diff_series = series.copy()
        num_diff = 0
        
        for i in range(max_diff + 1):
            if self.check_stationarity(diff_series):
                return diff_series, num_diff
            
            diff_series = diff_series.diff().dropna()
            num_diff += 1
        
        return diff_series, num_diff
    
    def auto_select_order(self, series, seasonal=False, max_p=5, max_q=5, max_d=2):
        """
        Automatically select optimal ARIMA order using AIC
        
        Args:
            series: Time series data
            seasonal: Whether to include seasonal components
            max_p: Maximum AR order
            max_q: Maximum MA order  
            max_d: Maximum differencing order
            
        Returns:
            Optimal order tuple
        """
        try:
            model = auto_arima(
                series,
                start_p=0, start_q=0,
                max_p=max_p, max_q=max_q, max_d=max_d,
                seasonal=seasonal,
                stepwise=True,
                suppress_warnings=True,
                error_action='ignore'
            )
            
            return model.order, model.seasonal_order if seasonal else None
            
        except Exception as e:
            print(f"Auto ARIMA failed: {e}")
            return (1, 1, 1), None
    
    def fit(self, series, auto_order=True):
        """
        Fit ARIMA model to time series data
        
        Args:
            series: Time series data
            auto_order: Whether to automatically select order
        """
        try:
            if auto_order and self.order is None:
                self.order, self.seasonal_order = self.auto_select_order(series)
                print(f"Auto-selected ARIMA order: {self.order}")
            
            # Fit the model
            self.model = ARIMA(series, order=self.order, seasonal_order=self.seasonal_order)
            self.fitted_model = self.model.fit()
            self.is_fitted = True
            
            print(f"ARIMA{self.order} model fitted successfully")
            print(f"AIC: {self.fitted_model.aic:.2f}")
            print(f"BIC: {self.fitted_model.bic:.2f}")
            
        except Exception as e:
            print(f"Error fitting ARIMA model: {e}")
            self.is_fitted = False
    
    def predict(self, steps=1, return_conf_int=True, alpha=0.05):
        """
        Make predictions using fitted ARIMA model
        
        Args:
            steps: Number of steps to forecast
            return_conf_int: Whether to return confidence intervals
            alpha: Significance level for confidence intervals
            
        Returns:
            Predictions and optionally confidence intervals
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        try:
            forecast = self.fitted_model.forecast(steps=steps, alpha=alpha)
            
            if return_conf_int:
                conf_int = self.fitted_model.get_forecast(steps=steps, alpha=alpha).conf_int()
                return forecast, conf_int
            else:
                return forecast
                
        except Exception as e:
            print(f"Error making predictions: {e}")
            return None
    
    def get_residuals(self):
        """
        Get model residuals for diagnostic analysis
        
        Returns:
            Residuals series
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        return self.fitted_model.resid
    
    def diagnostic_plots(self):
        """
        Generate diagnostic plots for model validation
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Residuals plot
        residuals = self.get_residuals()
        axes[0, 0].plot(residuals)
        axes[0, 0].set_title('Residuals')
        axes[0, 0].grid(True)
        
        # ACF of residuals
        from statsmodels.graphics.tsaplots import plot_acf
        plot_acf(residuals, ax=axes[0, 1], lags=20)
        axes[0, 1].set_title('ACF of Residuals')
        
        # Q-Q plot
        from scipy import stats
        stats.probplot(residuals, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('Q-Q Plot')
        
        # Histogram of residuals
        axes[1, 1].hist(residuals, bins=20, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('Residuals Distribution')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.show()
    
    def ljung_box_test(self, lags=10):
        """
        Perform Ljung-Box test for residual autocorrelation
        
        Args:
            lags: Number of lags to test
            
        Returns:
            Test statistic and p-value
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        residuals = self.get_residuals()
        result = acorr_ljungbox(residuals, lags=lags, return_df=True)
        
        return result


class ExponentialSmoothingPredictor:
    """
    Exponential Smoothing model for Bitcoin price prediction
    """
    
    def __init__(self, trend=None, seasonal=None, seasonal_periods=None):
        self.trend = trend
        self.seasonal = seasonal
        self.seasonal_periods = seasonal_periods
        self.model = None
        self.fitted_model = None
        self.is_fitted = False
    
    def fit(self, series):
        """
        Fit Exponential Smoothing model
        
        Args:
            series: Time series data
        """
        try:
            self.model = ExponentialSmoothing(
                series,
                trend=self.trend,
                seasonal=self.seasonal,
                seasonal_periods=self.seasonal_periods
            )
            
            self.fitted_model = self.model.fit()
            self.is_fitted = True
            
            print("Exponential Smoothing model fitted successfully")
            print(f"AIC: {self.fitted_model.aic:.2f}")
            
        except Exception as e:
            print(f"Error fitting Exponential Smoothing model: {e}")
            self.is_fitted = False
    
    def predict(self, steps=1):
        """
        Make predictions using fitted model
        
        Args:
            steps: Number of steps to forecast
            
        Returns:
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        try:
            forecast = self.fitted_model.forecast(steps=steps)
            return forecast
            
        except Exception as e:
            print(f"Error making predictions: {e}")
            return None


class TraditionalModelEnsemble:
    """
    Ensemble of traditional time series models
    """
    
    def __init__(self):
        self.models = {}
        self.weights = {}
        self.is_fitted = False
    
    def add_model(self, name, model, weight=1.0):
        """
        Add a model to the ensemble
        
        Args:
            name: Model name
            model: Model instance
            weight: Model weight in ensemble
        """
        self.models[name] = model
        self.weights[name] = weight
    
    def fit(self, series):
        """
        Fit all models in the ensemble
        
        Args:
            series: Time series data
        """
        print("Fitting ensemble models...")
        
        for name, model in self.models.items():
            print(f"Fitting {name}...")
            model.fit(series)
        
        self.is_fitted = True
        print("Ensemble fitting completed")
    
    def predict(self, steps=1):
        """
        Make ensemble predictions
        
        Args:
            steps: Number of steps to forecast
            
        Returns:
            Weighted ensemble predictions
        """
        if not self.is_fitted:
            raise ValueError("Ensemble must be fitted before making predictions")
        
        predictions = {}
        total_weight = sum(self.weights.values())
        
        # Get predictions from each model
        for name, model in self.models.items():
            if hasattr(model, 'is_fitted') and model.is_fitted:
                pred = model.predict(steps)
                if pred is not None:
                    predictions[name] = pred
        
        if not predictions:
            return None
        
        # Calculate weighted average
        ensemble_pred = np.zeros(steps)
        for name, pred in predictions.items():
            weight = self.weights[name] / total_weight
            ensemble_pred += weight * pred
        
        return ensemble_pred, predictions


def evaluate_traditional_models(data, target_col='Close', test_size=0.2):
    """
    Comprehensive evaluation of traditional models
    
    Args:
        data: DataFrame with time series data
        target_col: Target column name
        test_size: Proportion of data for testing
        
    Returns:
        Dictionary with model results
    """
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    
    # Split data
    split_idx = int(len(data) * (1 - test_size))
    train_data = data[target_col][:split_idx]
    test_data = data[target_col][split_idx:]
    
    results = {}
    
    # ARIMA Model
    print("Evaluating ARIMA model...")
    arima_model = ARIMAPredictor()
    arima_model.fit(train_data)
    
    if arima_model.is_fitted:
        arima_pred = arima_model.predict(steps=len(test_data), return_conf_int=False)
        
        results['ARIMA'] = {
            'predictions': arima_pred,
            'MAE': mean_absolute_error(test_data, arima_pred),
            'RMSE': np.sqrt(mean_squared_error(test_data, arima_pred)),
            'MAPE': np.mean(np.abs((test_data - arima_pred) / test_data)) * 100
        }
    
    # Exponential Smoothing
    print("Evaluating Exponential Smoothing model...")
    es_model = ExponentialSmoothingPredictor(trend='add')
    es_model.fit(train_data)
    
    if es_model.is_fitted:
        es_pred = es_model.predict(steps=len(test_data))
        
        results['Exponential_Smoothing'] = {
            'predictions': es_pred,
            'MAE': mean_absolute_error(test_data, es_pred),
            'RMSE': np.sqrt(mean_squared_error(test_data, es_pred)),
            'MAPE': np.mean(np.abs((test_data - es_pred) / test_data)) * 100
        }
    
    return results, test_data


if __name__ == "__main__":
    # Example usage
    import yfinance as yf
    
    # Get Bitcoin data
    btc = yf.Ticker("BTC-USD")
    data = btc.history(period="1y")
    
    # Evaluate models
    results, test_data = evaluate_traditional_models(data)
    
    # Print results
    for model_name, metrics in results.items():
        print(f"\n{model_name} Results:")
        print(f"MAE: {metrics['MAE']:.2f}")
        print(f"RMSE: {metrics['RMSE']:.2f}")
        print(f"MAPE: {metrics['MAPE']:.2f}%")
