# Bitcoin Price Prediction Project

## Objective
Develop a comprehensive time-series forecasting model for Bitcoin prices using publicly available data, implementing both traditional statistical methods and modern machine learning approaches.

## Project Structure
```
bitcoin-price-prediction/
├── data/
│   ├── raw/                    # Raw data from APIs
│   ├── processed/              # Cleaned and preprocessed data
│   └── features/               # Engineered features
├── src/
│   ├── data_collection/        # Data sourcing scripts
│   ├── preprocessing/          # Data cleaning and preprocessing
│   ├── feature_engineering/    # Feature creation modules
│   ├── models/                 # Model implementations
│   ├── evaluation/             # Evaluation metrics and frameworks
│   └── utils/                  # Utility functions
├── notebooks/
│   ├── 01_data_exploration.ipynb
│   ├── 02_feature_engineering.ipynb
│   ├── 03_model_development.ipynb
│   └── 04_evaluation_analysis.ipynb
├── models/                     # Saved model artifacts
├── results/                    # Model outputs and predictions
├── docs/                       # Documentation
├── tests/                      # Unit tests
├── requirements.txt            # Python dependencies
├── config.yaml                 # Configuration file
└── README.md                   # This file
```

## Key Components

### Data Sources
- **Primary**: Yahoo Finance (yfinance) - Free, reliable, extensive historical data
- **Secondary**: CoinGecko API - Additional market data and validation
- **Backup**: Alpha Vantage - Alternative data source

### Feature Engineering
1. **Time-based Features**
   - Lagged prices (1, 7, 30 days)
   - Moving averages (7, 14, 30, 50, 200 days)
   - Rolling statistics (volatility, min/max)

2. **Technical Indicators**
   - RSI (Relative Strength Index)
   - Bollinger Bands
   - MACD (Moving Average Convergence Divergence)
   - Stochastic Oscillator

3. **Volatility Measures**
   - Historical volatility
   - GARCH-based volatility
   - Realized volatility

### Models
1. **Statistical Models**
   - ARIMA/SARIMA
   - Auto-ARIMA with automated parameter selection

2. **Machine Learning Models**
   - Random Forest
   - XGBoost
   - LightGBM

3. **Deep Learning Models**
   - LSTM (Long Short-Term Memory)
   - GRU (Gated Recurrent Unit)
   - Transformer-based architectures

### Evaluation Metrics
- **Primary**: MAPE (Mean Absolute Percentage Error)
- **Secondary**: RMSE, MAE, Directional Accuracy
- **Trading Performance**: Sharpe Ratio, Maximum Drawdown, Win Rate

## Installation

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run setup script: `python setup.py`

## Usage

1. **Data Collection**: `python src/data_collection/collect_data.py`
2. **Feature Engineering**: `python src/feature_engineering/create_features.py`
3. **Model Training**: `python src/models/train_models.py`
4. **Evaluation**: `python src/evaluation/evaluate_models.py`

## Results
Results and model performance will be documented in the `results/` directory with comprehensive analysis and visualizations.

## Contributing
This project follows best practices for time series forecasting and financial modeling. Please ensure proper validation techniques are used to prevent data leakage.
