#!/usr/bin/env python3
"""
Production Prediction API for Bitcoin Price Prediction
=====================================================

This module provides a production-ready API for Bitcoin price predictions
using pre-trained models and real-time data processing.

Author: Bitcoin Price Prediction Production System
Date: 2025-07-07
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import pandas as pd
import numpy as np
import warnings
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import logging
import traceback

# Import our production modules
from production_model_manager import ProductionModelManager
from production_data_pipeline import ProductionDataPipeline

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables for managers
model_manager = None
data_pipeline = None

def initialize_system():
    """Initialize the production system components."""
    global model_manager, data_pipeline
    
    try:
        logger.info("Initializing Production Bitcoin Prediction System...")
        
        # Initialize model manager
        model_manager = ProductionModelManager()
        logger.info("✓ Model Manager initialized")
        
        # Initialize data pipeline
        data_pipeline = ProductionDataPipeline()
        logger.info("✓ Data Pipeline initialized")
        
        logger.info("🚀 Production system ready!")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize system: {str(e)}")
        logger.error(traceback.format_exc())
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        system_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'model_manager': model_manager is not None,
            'data_pipeline': data_pipeline is not None,
            'version': '1.0.0'
        }
        
        if model_manager:
            system_status['models_loaded'] = len(model_manager.models)
        
        return jsonify(system_status), 200
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/models/summary', methods=['GET'])
def get_models_summary():
    """Get summary of all loaded models."""
    try:
        if not model_manager:
            return jsonify({'error': 'Model manager not initialized'}), 500
        
        summary = model_manager.get_model_summary()
        return jsonify(summary), 200
        
    except Exception as e:
        logger.error(f"Error getting models summary: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/data/summary', methods=['GET'])
def get_data_summary():
    """Get summary of data pipeline status."""
    try:
        if not data_pipeline:
            return jsonify({'error': 'Data pipeline not initialized'}), 500
        
        summary = data_pipeline.get_data_summary()
        return jsonify(summary), 200
        
    except Exception as e:
        logger.error(f"Error getting data summary: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/predict/arima', methods=['GET'])
def predict_arima():
    """Get ARIMA predictions for Bitcoin price and returns."""
    try:
        if not model_manager:
            return jsonify({'error': 'Model manager not initialized'}), 500
        
        # Get prediction steps from query parameters
        steps = request.args.get('steps', default=7, type=int)
        steps = min(max(steps, 1), 30)  # Limit between 1 and 30 days
        
        predictions = model_manager.predict_arima(steps=steps)
        
        if not predictions:
            return jsonify({'error': 'Failed to generate ARIMA predictions'}), 500
        
        return jsonify({
            'model_type': 'ARIMA',
            'prediction_steps': steps,
            'predictions': predictions,
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error in ARIMA prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/predict/tree/<timeframe>', methods=['GET'])
def predict_tree_models(timeframe):
    """Get tree model predictions for a specific timeframe."""
    try:
        if not model_manager or not data_pipeline:
            return jsonify({'error': 'System not fully initialized'}), 500
        
        # Validate timeframe
        if timeframe not in ['1d', '7d', '30d']:
            return jsonify({'error': 'Invalid timeframe. Use 1d, 7d, or 30d'}), 400
        
        # Get latest features for prediction
        features = data_pipeline.get_prediction_features()
        
        if features.empty:
            return jsonify({'error': 'Could not prepare prediction features'}), 500
        
        # Get predictions from tree models
        predictions = model_manager.predict_tree_models(features, timeframe)
        
        if not predictions:
            return jsonify({'error': 'Failed to generate tree model predictions'}), 500
        
        return jsonify({
            'model_type': 'tree_models',
            'timeframe': timeframe,
            'predictions': predictions,
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error in tree model prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/predict/ensemble/<timeframe>', methods=['GET'])
def predict_ensemble(timeframe):
    """Get ensemble predictions combining all tree models."""
    try:
        if not model_manager or not data_pipeline:
            return jsonify({'error': 'System not fully initialized'}), 500
        
        # Validate timeframe
        if timeframe not in ['1d', '7d', '30d']:
            return jsonify({'error': 'Invalid timeframe. Use 1d, 7d, or 30d'}), 400
        
        # Get latest features for prediction
        features = data_pipeline.get_prediction_features()
        
        if features.empty:
            return jsonify({'error': 'Could not prepare prediction features'}), 500
        
        # Get ensemble predictions
        predictions = model_manager.get_ensemble_prediction(features, timeframe)
        
        if not predictions:
            return jsonify({'error': 'Failed to generate ensemble predictions'}), 500
        
        return jsonify({
            'model_type': 'ensemble',
            'timeframe': timeframe,
            'predictions': predictions,
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error in ensemble prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/predict/all', methods=['GET'])
def predict_all():
    """Get predictions from all models and timeframes."""
    try:
        if not model_manager or not data_pipeline:
            return jsonify({'error': 'System not fully initialized'}), 500
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'predictions': {}
        }
        
        # ARIMA predictions
        try:
            arima_pred = model_manager.predict_arima(steps=7)
            if arima_pred:
                results['predictions']['arima'] = arima_pred
        except Exception as e:
            logger.warning(f"ARIMA prediction failed: {str(e)}")
            results['predictions']['arima'] = {'error': str(e)}
        
        # Tree model predictions for all timeframes
        features = data_pipeline.get_prediction_features()
        
        if not features.empty:
            for timeframe in ['1d', '7d', '30d']:
                try:
                    ensemble_pred = model_manager.get_ensemble_prediction(features, timeframe)
                    if ensemble_pred:
                        results['predictions'][f'ensemble_{timeframe}'] = ensemble_pred
                except Exception as e:
                    logger.warning(f"Ensemble prediction for {timeframe} failed: {str(e)}")
                    results['predictions'][f'ensemble_{timeframe}'] = {'error': str(e)}
        else:
            results['predictions']['tree_models'] = {'error': 'Could not prepare features'}
        
        return jsonify(results), 200
        
    except Exception as e:
        logger.error(f"Error in comprehensive prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/performance/<model_type>', methods=['GET'])
def get_model_performance(model_type):
    """Get performance metrics for a specific model type."""
    try:
        if not model_manager:
            return jsonify({'error': 'Model manager not initialized'}), 500
        
        timeframe = request.args.get('timeframe', default='1d')
        
        if model_type == 'arima':
            performance = model_manager.get_model_performance('arima')
        elif model_type in ['random_forest', 'xgboost', 'lightgbm']:
            if timeframe not in ['1d', '7d', '30d']:
                return jsonify({'error': 'Invalid timeframe for tree models'}), 400
            performance = model_manager.get_model_performance(model_type, timeframe)
        else:
            return jsonify({'error': 'Invalid model type'}), 400
        
        return jsonify({
            'model_type': model_type,
            'timeframe': timeframe if model_type != 'arima' else 'N/A',
            'performance': performance,
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting model performance: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({
        'error': 'Endpoint not found',
        'available_endpoints': [
            '/health',
            '/models/summary',
            '/data/summary',
            '/predict/arima',
            '/predict/tree/<timeframe>',
            '/predict/ensemble/<timeframe>',
            '/predict/all',
            '/performance/<model_type>'
        ]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({
        'error': 'Internal server error',
        'timestamp': datetime.now().isoformat()
    }), 500

if __name__ == '__main__':
    # Initialize the system
    if initialize_system():
        logger.info("Starting Production Bitcoin Prediction API...")
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        logger.error("Failed to initialize system. Exiting.")
        exit(1)
