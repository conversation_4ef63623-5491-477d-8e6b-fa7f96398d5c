"""Time Series Train-Test Split Strategies for Bitcoin Price Prediction."""

import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.model_selection import TimeSeriesSplit
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_preprocessed_data():
    """Load the preprocessed Bitcoin data."""
    data_path = Path('data/processed/bitcoin_preprocessed.csv')
    if not data_path.exists():
        raise FileNotFoundError("Preprocessed data not found. Run preprocessing first.")
    
    df = pd.read_csv(data_path, index_col=0, parse_dates=True)
    print(f"Loaded preprocessed data shape: {df.shape}")
    return df

def simple_train_test_split(df, test_size=0.2, target_cols=None):
    """Simple chronological train-test split."""
    print(f"\n" + "="*50)
    print("SIMPLE TRAIN-TEST SPLIT")
    print("="*50)
    
    if target_cols is None:
        target_cols = [col for col in df.columns if 'target' in col]
    
    feature_cols = [col for col in df.columns if col not in target_cols]
    
    # Calculate split point
    split_idx = int(len(df) * (1 - test_size))
    split_date = df.index[split_idx]
    
    # Split data
    train_data = df.iloc[:split_idx]
    test_data = df.iloc[split_idx:]
    
    X_train = train_data[feature_cols]
    y_train = train_data[target_cols]
    X_test = test_data[feature_cols]
    y_test = test_data[target_cols]
    
    print(f"Split date: {split_date}")
    print(f"Train period: {train_data.index.min()} to {train_data.index.max()}")
    print(f"Test period: {test_data.index.min()} to {test_data.index.max()}")
    print(f"Train size: {len(train_data)} ({(1-test_size)*100:.1f}%)")
    print(f"Test size: {len(test_data)} ({test_size*100:.1f}%)")
    
    return {
        'X_train': X_train,
        'y_train': y_train,
        'X_test': X_test,
        'y_test': y_test,
        'split_date': split_date,
        'train_dates': train_data.index,
        'test_dates': test_data.index
    }

def walk_forward_validation_split(df, n_splits=5, target_cols=None):
    """Walk-forward validation split for time series."""
    print(f"\n" + "="*50)
    print("WALK-FORWARD VALIDATION SPLIT")
    print("="*50)
    
    if target_cols is None:
        target_cols = [col for col in df.columns if 'target' in col]
    
    feature_cols = [col for col in df.columns if col not in target_cols]
    
    # Use TimeSeriesSplit
    tscv = TimeSeriesSplit(n_splits=n_splits)
    
    splits = []
    for i, (train_idx, test_idx) in enumerate(tscv.split(df)):
        train_data = df.iloc[train_idx]
        test_data = df.iloc[test_idx]
        
        split_info = {
            'fold': i + 1,
            'X_train': train_data[feature_cols],
            'y_train': train_data[target_cols],
            'X_test': test_data[feature_cols],
            'y_test': test_data[target_cols],
            'train_start': train_data.index.min(),
            'train_end': train_data.index.max(),
            'test_start': test_data.index.min(),
            'test_end': test_data.index.max(),
            'train_size': len(train_data),
            'test_size': len(test_data)
        }
        
        splits.append(split_info)
        
        print(f"Fold {i+1}:")
        print(f"  Train: {split_info['train_start']} to {split_info['train_end']} ({split_info['train_size']} samples)")
        print(f"  Test:  {split_info['test_start']} to {split_info['test_end']} ({split_info['test_size']} samples)")
    
    return splits

def expanding_window_validation(df, initial_train_size=0.5, step_size=30, target_cols=None):
    """Expanding window validation for time series."""
    print(f"\n" + "="*50)
    print("EXPANDING WINDOW VALIDATION")
    print("="*50)
    
    if target_cols is None:
        target_cols = [col for col in df.columns if 'target' in col]
    
    feature_cols = [col for col in df.columns if col not in target_cols]
    
    # Initial training size
    initial_size = int(len(df) * initial_train_size)
    
    splits = []
    fold = 1
    
    # Start from initial training size and expand
    for train_end_idx in range(initial_size, len(df) - step_size, step_size):
        test_start_idx = train_end_idx
        test_end_idx = min(train_end_idx + step_size, len(df))
        
        train_data = df.iloc[:train_end_idx]
        test_data = df.iloc[test_start_idx:test_end_idx]
        
        if len(test_data) < step_size // 2:  # Skip if test set too small
            continue
        
        split_info = {
            'fold': fold,
            'X_train': train_data[feature_cols],
            'y_train': train_data[target_cols],
            'X_test': test_data[feature_cols],
            'y_test': test_data[target_cols],
            'train_start': train_data.index.min(),
            'train_end': train_data.index.max(),
            'test_start': test_data.index.min(),
            'test_end': test_data.index.max(),
            'train_size': len(train_data),
            'test_size': len(test_data)
        }
        
        splits.append(split_info)
        
        print(f"Fold {fold}:")
        print(f"  Train: {split_info['train_start']} to {split_info['train_end']} ({split_info['train_size']} samples)")
        print(f"  Test:  {split_info['test_start']} to {split_info['test_end']} ({split_info['test_size']} samples)")
        
        fold += 1
    
    return splits

def sliding_window_validation(df, train_window=365, test_window=30, step_size=30, target_cols=None):
    """Sliding window validation for time series."""
    print(f"\n" + "="*50)
    print("SLIDING WINDOW VALIDATION")
    print("="*50)
    
    if target_cols is None:
        target_cols = [col for col in df.columns if 'target' in col]
    
    feature_cols = [col for col in df.columns if col not in target_cols]
    
    splits = []
    fold = 1
    
    # Slide the window through the data
    for start_idx in range(0, len(df) - train_window - test_window, step_size):
        train_start_idx = start_idx
        train_end_idx = start_idx + train_window
        test_start_idx = train_end_idx
        test_end_idx = test_start_idx + test_window
        
        if test_end_idx > len(df):
            break
        
        train_data = df.iloc[train_start_idx:train_end_idx]
        test_data = df.iloc[test_start_idx:test_end_idx]
        
        split_info = {
            'fold': fold,
            'X_train': train_data[feature_cols],
            'y_train': train_data[target_cols],
            'X_test': test_data[feature_cols],
            'y_test': test_data[target_cols],
            'train_start': train_data.index.min(),
            'train_end': train_data.index.max(),
            'test_start': test_data.index.min(),
            'test_end': test_data.index.max(),
            'train_size': len(train_data),
            'test_size': len(test_data)
        }
        
        splits.append(split_info)
        
        print(f"Fold {fold}:")
        print(f"  Train: {split_info['train_start']} to {split_info['train_end']} ({split_info['train_size']} samples)")
        print(f"  Test:  {split_info['test_start']} to {split_info['test_end']} ({split_info['test_size']} samples)")
        
        fold += 1
    
    return splits

def visualize_splits(df, splits, split_type="Time Series CV"):
    """Visualize the train-test splits."""
    print(f"\n" + "="*50)
    print("VISUALIZING SPLITS")
    print("="*50)
    
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # Plot Bitcoin price
    ax.plot(df.index, df['close'], alpha=0.3, color='gray', label='Bitcoin Price')
    
    # Color map for different folds
    colors = plt.cm.Set3(np.linspace(0, 1, len(splits)))
    
    for i, split in enumerate(splits):
        # Plot training period
        train_mask = (df.index >= split['train_start']) & (df.index <= split['train_end'])
        ax.fill_between(df.index[train_mask], 
                       df['close'].min(), df['close'].max(),
                       alpha=0.3, color=colors[i], label=f'Fold {split["fold"]} Train')
        
        # Plot test period
        test_mask = (df.index >= split['test_start']) & (df.index <= split['test_end'])
        ax.fill_between(df.index[test_mask], 
                       df['close'].min(), df['close'].max(),
                       alpha=0.6, color=colors[i], label=f'Fold {split["fold"]} Test')
    
    ax.set_title(f'{split_type} - Train/Test Splits Visualization', fontsize=16, fontweight='bold')
    ax.set_xlabel('Date')
    ax.set_ylabel('Bitcoin Price (USD)')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'results/{split_type.lower().replace(" ", "_")}_splits.png', 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Split visualization saved to: results/{split_type.lower().replace(' ', '_')}_splits.png")

def save_splits(splits, split_type, target_horizon='1d'):
    """Save train-test splits to files."""
    print(f"\n" + "="*50)
    print(f"SAVING {split_type.upper()} SPLITS")
    print("="*50)
    
    # Create splits directory
    splits_dir = Path(f'data/splits/{split_type.lower().replace(" ", "_")}')
    splits_dir.mkdir(parents=True, exist_ok=True)
    
    for split in splits:
        fold_dir = splits_dir / f'fold_{split["fold"]}'
        fold_dir.mkdir(exist_ok=True)
        
        # Save training data
        split['X_train'].to_csv(fold_dir / 'X_train.csv')
        split['y_train'].to_csv(fold_dir / 'y_train.csv')
        
        # Save test data
        split['X_test'].to_csv(fold_dir / 'X_test.csv')
        split['y_test'].to_csv(fold_dir / 'y_test.csv')
        
        # Save metadata
        metadata = {
            'fold': split['fold'],
            'train_start': str(split['train_start']),
            'train_end': str(split['train_end']),
            'test_start': str(split['test_start']),
            'test_end': str(split['test_end']),
            'train_size': split['train_size'],
            'test_size': split['test_size']
        }
        
        import json
        with open(fold_dir / 'metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
    
    print(f"✓ Saved {len(splits)} folds to: {splits_dir}")

def main():
    """Main train-test split function."""
    print("Bitcoin Price Prediction - Train-Test Split Strategies")
    print("=" * 60)
    
    try:
        # Load preprocessed data
        df = load_preprocessed_data()
        
        # Create results directory
        Path('results').mkdir(exist_ok=True)
        
        # 1. Simple train-test split
        simple_split = simple_train_test_split(df, test_size=0.2)
        
        # 2. Walk-forward validation
        wf_splits = walk_forward_validation_split(df, n_splits=5)
        visualize_splits(df, wf_splits, "Walk Forward Validation")
        save_splits(wf_splits, "walk_forward")
        
        # 3. Expanding window validation
        ew_splits = expanding_window_validation(df, initial_train_size=0.6, step_size=60)
        visualize_splits(df, ew_splits, "Expanding Window Validation")
        save_splits(ew_splits, "expanding_window")
        
        # 4. Sliding window validation
        sw_splits = sliding_window_validation(df, train_window=730, test_window=60, step_size=60)
        visualize_splits(df, sw_splits, "Sliding Window Validation")
        save_splits(sw_splits, "sliding_window")
        
        # Save simple split as well
        simple_splits = [{
            'fold': 1,
            'X_train': simple_split['X_train'],
            'y_train': simple_split['y_train'],
            'X_test': simple_split['X_test'],
            'y_test': simple_split['y_test'],
            'train_start': simple_split['train_dates'].min(),
            'train_end': simple_split['train_dates'].max(),
            'test_start': simple_split['test_dates'].min(),
            'test_end': simple_split['test_dates'].max(),
            'train_size': len(simple_split['X_train']),
            'test_size': len(simple_split['X_test'])
        }]
        save_splits(simple_splits, "simple_split")
        
        print(f"\n" + "="*60)
        print("SPLIT STRATEGIES SUMMARY")
        print("="*60)
        print(f"Simple Split: 1 fold")
        print(f"Walk-Forward: {len(wf_splits)} folds")
        print(f"Expanding Window: {len(ew_splits)} folds")
        print(f"Sliding Window: {len(sw_splits)} folds")
        
        print("\n" + "="*60)
        print("TRAIN-TEST SPLIT STRATEGIES COMPLETED!")
        print("="*60)
        
    except Exception as e:
        print(f"Error during train-test split: {e}")

if __name__ == "__main__":
    main()
