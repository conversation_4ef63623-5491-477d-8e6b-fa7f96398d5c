"""Comprehensive Data Preprocessing for Bitcoin Price Prediction."""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer, KNNImputer
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_engineered_features():
    """Load the engineered features dataset."""
    data_path = Path('data/features/bitcoin_engineered_features.csv')
    if not data_path.exists():
        raise FileNotFoundError("Engineered features not found. Run feature engineering first.")
    
    df = pd.read_csv(data_path, index_col=0, parse_dates=True)
    print(f"Loaded engineered features shape: {df.shape}")
    return df

def check_data_quality(df):
    """Perform comprehensive data quality checks."""
    print("\n" + "="*50)
    print("DATA QUALITY ASSESSMENT")
    print("="*50)
    
    # Check for missing values
    missing_counts = df.isnull().sum()
    missing_pct = (missing_counts / len(df)) * 100
    
    print("Missing Values Analysis:")
    if missing_counts.sum() > 0:
        missing_df = pd.DataFrame({
            'Missing_Count': missing_counts[missing_counts > 0],
            'Missing_Percentage': missing_pct[missing_pct > 0]
        }).sort_values('Missing_Percentage', ascending=False)
        print(missing_df.head(10))
    else:
        print("✓ No missing values found")
    
    # Check for infinite values
    inf_counts = np.isinf(df.select_dtypes(include=[np.number])).sum()
    if inf_counts.sum() > 0:
        print(f"\nInfinite Values Found:")
        print(inf_counts[inf_counts > 0])
    else:
        print("✓ No infinite values found")
    
    # Check for duplicate rows
    duplicates = df.duplicated().sum()
    print(f"\nDuplicate rows: {duplicates}")
    
    # Check data types
    print(f"\nData types:")
    print(df.dtypes.value_counts())
    
    # Check for constant features
    constant_features = []
    for col in df.select_dtypes(include=[np.number]).columns:
        if df[col].nunique() <= 1:
            constant_features.append(col)
    
    if constant_features:
        print(f"\nConstant features found: {constant_features}")
    else:
        print("✓ No constant features found")
    
    return df

def detect_outliers(df, method='iqr', threshold=3):
    """Detect outliers in the dataset."""
    print(f"\n" + "="*50)
    print(f"OUTLIER DETECTION ({method.upper()} method)")
    print("="*50)
    
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    outlier_counts = {}
    
    for col in numeric_cols:
        if method == 'iqr':
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = (df[col] < lower_bound) | (df[col] > upper_bound)
        
        elif method == 'zscore':
            z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
            outliers = z_scores > threshold
        
        outlier_counts[col] = outliers.sum()
    
    # Display top outlier columns
    outlier_df = pd.DataFrame(list(outlier_counts.items()), 
                             columns=['Feature', 'Outlier_Count'])
    outlier_df['Outlier_Percentage'] = (outlier_df['Outlier_Count'] / len(df)) * 100
    outlier_df = outlier_df.sort_values('Outlier_Count', ascending=False)
    
    print("Top 10 features with most outliers:")
    print(outlier_df.head(10))
    
    return outlier_df

def handle_missing_values(df, strategy='knn'):
    """Handle missing values in the dataset."""
    print(f"\n" + "="*50)
    print(f"HANDLING MISSING VALUES ({strategy.upper()} strategy)")
    print("="*50)
    
    missing_before = df.isnull().sum().sum()
    
    if missing_before == 0:
        print("✓ No missing values to handle")
        return df
    
    # Separate numeric and categorical columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    categorical_cols = df.select_dtypes(exclude=[np.number]).columns
    
    if strategy == 'knn':
        # Use KNN imputer for numeric columns
        if len(numeric_cols) > 0:
            imputer = KNNImputer(n_neighbors=5)
            df[numeric_cols] = imputer.fit_transform(df[numeric_cols])
        
        # Use mode for categorical columns
        if len(categorical_cols) > 0:
            for col in categorical_cols:
                df[col].fillna(df[col].mode()[0], inplace=True)
    
    elif strategy == 'forward_fill':
        # Forward fill (good for time series)
        df = df.fillna(method='ffill')
        # Backward fill for any remaining NaNs
        df = df.fillna(method='bfill')
    
    elif strategy == 'interpolate':
        # Linear interpolation for numeric columns
        df[numeric_cols] = df[numeric_cols].interpolate(method='linear')
        # Forward fill for categorical
        if len(categorical_cols) > 0:
            df[categorical_cols] = df[categorical_cols].fillna(method='ffill')
    
    missing_after = df.isnull().sum().sum()
    print(f"Missing values before: {missing_before}")
    print(f"Missing values after: {missing_after}")
    print(f"✓ Handled {missing_before - missing_after} missing values")
    
    return df

def handle_outliers(df, outlier_df, treatment='cap', percentile=0.01):
    """Handle outliers in the dataset."""
    print(f"\n" + "="*50)
    print(f"HANDLING OUTLIERS ({treatment.upper()} method)")
    print("="*50)
    
    # Focus on features with significant outliers (>5% of data)
    significant_outliers = outlier_df[outlier_df['Outlier_Percentage'] > 5]['Feature'].tolist()
    
    if not significant_outliers:
        print("✓ No significant outliers to handle")
        return df
    
    print(f"Treating outliers in {len(significant_outliers)} features")
    
    for col in significant_outliers:
        if treatment == 'cap':
            # Cap outliers at percentiles
            lower_cap = df[col].quantile(percentile)
            upper_cap = df[col].quantile(1 - percentile)
            df[col] = np.clip(df[col], lower_cap, upper_cap)
        
        elif treatment == 'log':
            # Log transformation (for positive values only)
            if (df[col] > 0).all():
                df[col] = np.log1p(df[col])
        
        elif treatment == 'winsorize':
            # Winsorization
            lower_bound = df[col].quantile(percentile)
            upper_bound = df[col].quantile(1 - percentile)
            df[col] = np.where(df[col] < lower_bound, lower_bound, df[col])
            df[col] = np.where(df[col] > upper_bound, upper_bound, df[col])
    
    print(f"✓ Treated outliers in {len(significant_outliers)} features")
    return df

def scale_features(df, target_col='close', method='standard'):
    """Scale features for machine learning."""
    print(f"\n" + "="*50)
    print(f"FEATURE SCALING ({method.upper()} method)")
    print("="*50)
    
    # Separate features and target
    feature_cols = [col for col in df.columns if col != target_col]
    
    # Select scaler
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'minmax':
        scaler = MinMaxScaler()
    elif method == 'robust':
        scaler = RobustScaler()
    else:
        raise ValueError(f"Unknown scaling method: {method}")
    
    # Fit and transform features
    df_scaled = df.copy()
    df_scaled[feature_cols] = scaler.fit_transform(df[feature_cols])
    
    print(f"✓ Scaled {len(feature_cols)} features using {method} scaling")
    
    # Save scaler for future use
    import joblib
    scaler_path = f'models/scaler_{method}.joblib'
    Path('models').mkdir(exist_ok=True)
    joblib.dump(scaler, scaler_path)
    print(f"✓ Scaler saved to: {scaler_path}")
    
    return df_scaled, scaler

def create_target_variables(df, target_col='close', horizons=[1, 7, 30]):
    """Create target variables for different prediction horizons."""
    print(f"\n" + "="*50)
    print("CREATING TARGET VARIABLES")
    print("="*50)
    
    for horizon in horizons:
        # Price target (future price)
        df[f'target_price_{horizon}d'] = df[target_col].shift(-horizon)
        
        # Return target (future return)
        df[f'target_return_{horizon}d'] = (df[f'target_price_{horizon}d'] / df[target_col]) - 1
        
        # Direction target (up/down)
        df[f'target_direction_{horizon}d'] = (df[f'target_return_{horizon}d'] > 0).astype(int)
    
    print(f"✓ Created target variables for horizons: {horizons} days")
    return df

def ensure_chronological_order(df):
    """Ensure data is in chronological order."""
    print(f"\n" + "="*50)
    print("ENSURING CHRONOLOGICAL ORDER")
    print("="*50)
    
    if df.index.is_monotonic_increasing:
        print("✓ Data is already in chronological order")
    else:
        df = df.sort_index()
        print("✓ Data sorted in chronological order")
    
    # Check for gaps in time series
    date_diff = df.index.to_series().diff()
    expected_freq = date_diff.mode()[0]  # Most common frequency
    gaps = date_diff[date_diff != expected_freq].dropna()
    
    if len(gaps) > 0:
        print(f"⚠ Found {len(gaps)} gaps in time series")
        print(f"Expected frequency: {expected_freq}")
    else:
        print("✓ No gaps found in time series")
    
    return df

def main():
    """Main preprocessing function."""
    print("Bitcoin Price Prediction - Data Preprocessing")
    print("=" * 60)
    
    try:
        # Load engineered features
        df = load_engineered_features()
        
        # Data quality assessment
        df = check_data_quality(df)
        
        # Outlier detection
        outlier_df = detect_outliers(df, method='iqr')
        
        # Handle missing values
        df = handle_missing_values(df, strategy='interpolate')
        
        # Handle outliers
        df = handle_outliers(df, outlier_df, treatment='cap')
        
        # Ensure chronological order
        df = ensure_chronological_order(df)
        
        # Create target variables
        df = create_target_variables(df, target_col='close', horizons=[1, 7, 30])
        
        # Scale features
        df_scaled, scaler = scale_features(df, target_col='close', method='standard')
        
        # Remove rows with NaN targets (due to future shifts)
        initial_rows = len(df_scaled)
        df_scaled = df_scaled.dropna()
        final_rows = len(df_scaled)
        
        print(f"\n" + "="*60)
        print("PREPROCESSING SUMMARY")
        print("="*60)
        print(f"Initial rows: {initial_rows}")
        print(f"Final rows: {final_rows}")
        print(f"Rows removed: {initial_rows - final_rows}")
        print(f"Total features: {len(df_scaled.columns)}")
        print(f"Target variables: {len([col for col in df_scaled.columns if 'target' in col])}")
        
        # Save preprocessed data
        output_path = 'data/processed/bitcoin_preprocessed.csv'
        df_scaled.to_csv(output_path)
        print(f"✓ Preprocessed data saved to: {output_path}")
        
        # Save unscaled version as well
        unscaled_path = 'data/processed/bitcoin_preprocessed_unscaled.csv'
        df.dropna().to_csv(unscaled_path)
        print(f"✓ Unscaled preprocessed data saved to: {unscaled_path}")
        
        print("\n" + "="*60)
        print("DATA PREPROCESSING COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        return df_scaled
        
    except Exception as e:
        print(f"Error during preprocessing: {e}")
        return None

if __name__ == "__main__":
    main()
