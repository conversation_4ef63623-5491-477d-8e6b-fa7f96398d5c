# Essential imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
import warnings
from datetime import datetime, timedelta
import joblib
import json
from pathlib import Path

# ML libraries
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import xgboost as xgb
import lightgbm as lgb

# Time series
from statsmodels.tsa.arima.model import ARIMA
import pmdarima as pm

# Setup
warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("📊 All libraries loaded successfully!")
print(f"🕐 Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data(period="5y"):
    """Get Bitcoin data from Yahoo Finance"""
    print(f"📈 Fetching Bitcoin data for the last {period}...")
    
    btc = yf.download('BTC-USD', period=period, interval='1d')
    # Handle column names (yfinance returns tuples for single ticker)
    if isinstance(btc.columns[0], tuple):
        btc.columns = [col[0].lower() for col in btc.columns]
    else:
        btc.columns = [col.lower() for col in btc.columns]
    
    print(f"✅ Got {len(btc)} days of data")
    print(f"📅 Date range: {btc.index[0].date()} to {btc.index[-1].date()}")
    print(f"💰 Price range: ${btc['close'].min():,.0f} - ${btc['close'].max():,.0f}")
    
    return btc

# Get the data
btc_data = get_bitcoin_data()
btc_data.head()

# Quick look at the data
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Price over time
axes[0,0].plot(btc_data.index, btc_data['close'], linewidth=1)
axes[0,0].set_title('Bitcoin Price Over Time')
axes[0,0].set_ylabel('Price ($)')
axes[0,0].tick_params(axis='x', rotation=45)

# Volume
axes[0,1].plot(btc_data.index, btc_data['volume'], color='orange', alpha=0.7)
axes[0,1].set_title('Trading Volume')
axes[0,1].set_ylabel('Volume')
axes[0,1].tick_params(axis='x', rotation=45)

# Daily returns
returns = btc_data['close'].pct_change().dropna()
axes[1,0].hist(returns, bins=50, alpha=0.7, color='green')
axes[1,0].set_title('Daily Returns Distribution')
axes[1,0].set_xlabel('Daily Return')

# Price vs Volume scatter
axes[1,1].scatter(btc_data['volume'], btc_data['close'], alpha=0.5, s=1)
axes[1,1].set_title('Price vs Volume')
axes[1,1].set_xlabel('Volume')
axes[1,1].set_ylabel('Price ($)')

plt.tight_layout()
plt.show()

print(f"📊 Basic stats:")
print(f"   Average daily return: {returns.mean():.3f} ({returns.mean()*100:.2f}%)")
print(f"   Daily volatility: {returns.std():.3f} ({returns.std()*100:.2f}%)")
print(f"   Annualized volatility: {returns.std() * np.sqrt(365):.3f} ({returns.std() * np.sqrt(365)*100:.1f}%)")

def create_features(data):
    """Create features for Bitcoin prediction"""
    df = data.copy()
    
    print("🔧 Creating features...")
    
    # Basic price features
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    
    # Lagged prices (what happened before)
    for lag in [1, 2, 3, 7, 14, 30]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
    
    # Moving averages (trend indicators)
    for window in [7, 14, 30, 50, 100, 200]:
        df[f'ma_{window}'] = df['close'].rolling(window).mean()
        df[f'price_to_ma_{window}'] = df['close'] / df[f'ma_{window}']
    
    # Volatility features
    for window in [7, 14, 30]:
        df[f'volatility_{window}'] = df['returns'].rolling(window).std()
        df[f'high_low_ratio_{window}'] = (df['high'] / df['low']).rolling(window).mean()
    
    # Technical indicators - RSI
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    df['rsi_14'] = calculate_rsi(df['close'])
    
    # Bollinger Bands
    bb_window = 20
    bb_ma = df['close'].rolling(bb_window).mean()
    bb_std = df['close'].rolling(bb_window).std()
    df['bb_upper'] = bb_ma + (bb_std * 2)
    df['bb_lower'] = bb_ma - (bb_std * 2)
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # Volume features
    df['volume_ma_7'] = df['volume'].rolling(7).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma_7']
    
    # Time features
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month
    df['quarter'] = df.index.quarter
    
    # Target variables (what we want to predict)
    df['target_return_1d'] = df['returns'].shift(-1)  # Next day return
    df['target_return_7d'] = (df['close'].shift(-7) / df['close']) - 1  # 7-day return
    df['target_return_30d'] = (df['close'].shift(-30) / df['close']) - 1  # 30-day return
    
    print(f"✅ Created {len(df.columns)} features")
    return df

# Create features
btc_features = create_features(btc_data)

# Show some key features
key_features = ['close', 'returns', 'ma_30', 'rsi_14', 'volatility_7', 'target_return_1d']
print("\n📊 Sample of key features:")
btc_features[key_features].tail(10)

# Load existing trained models
print("🔄 Loading existing production models...")

try:
    # Load models from the models directory
    models_dir = Path('models')
    
    # Load ARIMA models
    arima_price_model = joblib.load(models_dir / 'arima_price_model.joblib')
    arima_returns_model = joblib.load(models_dir / 'arima_returns_model.joblib')
    
    # Load tree models
    rf_1d_model = joblib.load(models_dir / 'randomforest_target_return_1d_model.joblib')
    xgb_1d_model = joblib.load(models_dir / 'xgboost_target_return_1d_model.joblib')
    lgb_1d_model = joblib.load(models_dir / 'lightgbm_target_return_1d_model.joblib')
    
    # Load scaler
    production_scaler = joblib.load(models_dir / 'scaler_standard.joblib')
    
    print("✅ All production models loaded successfully!")
    
    # Load existing results
    with open('results/tree_models_results.json', 'r') as f:
        production_results = json.load(f)
    
    print("📊 Production model performance:")
    for model_name, results in production_results.items():
        if 'target_return_1d' in results:
            perf = results['target_return_1d']
            print(f"   {model_name}: R² = {perf['r2_score']:.3f}, RMSE = {perf['rmse']:.4f}")
            
except Exception as e:
    print(f"⚠️ Could not load production models: {str(e)}")
    print("   Make sure you've run the training scripts first!")

# Create a simple prediction function
def make_ensemble_prediction(latest_data):
    """Make ensemble prediction using production models"""
    
    try:
        # Prepare features (using the same feature engineering)
        features_df = create_features(latest_data)
        
        # Get the latest row with all features
        latest_features = features_df.dropna().iloc[-1:]
        
        # Select the same features used in training
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'adj close', 
                       'target_return_1d', 'target_return_7d', 'target_return_30d']
        feature_cols = [col for col in latest_features.columns if col not in exclude_cols]
        
        X_latest = latest_features[feature_cols]
        X_latest_scaled = production_scaler.transform(X_latest)
        
        # Make predictions with each model
        rf_pred = rf_1d_model.predict(X_latest_scaled)[0]
        xgb_pred = xgb_1d_model.predict(X_latest_scaled)[0]
        lgb_pred = lgb_1d_model.predict(X_latest_scaled)[0]
        
        # Ensemble prediction (simple average)
        ensemble_pred = (rf_pred + xgb_pred + lgb_pred) / 3
        
        # ARIMA prediction for price
        current_price = latest_data['close'].iloc[-1]
        arima_forecast = arima_price_model.forecast(steps=1)[0]
        arima_return = (arima_forecast / current_price) - 1
        
        results = {
            'current_price': current_price,
            'ensemble_return_prediction': ensemble_pred,
            'arima_return_prediction': arima_return,
            'arima_price_prediction': arima_forecast,
            'individual_predictions': {
                'random_forest': rf_pred,
                'xgboost': xgb_pred,
                'lightgbm': lgb_pred
            }
        }
        
        return results
        
    except Exception as e:
        print(f"❌ Prediction failed: {str(e)}")
        return None

# Test the prediction system
print("🔮 Testing prediction system with latest data...")
prediction_results = make_ensemble_prediction(btc_data)

if prediction_results:
    print(f"\n📈 Current Bitcoin Price: ${prediction_results['current_price']:,.2f}")
    print(f"🎯 Ensemble Return Prediction: {prediction_results['ensemble_return_prediction']:.4f} ({prediction_results['ensemble_return_prediction']*100:.2f}%)")
    print(f"📊 ARIMA Price Prediction: ${prediction_results['arima_price_prediction']:,.2f}")
    print(f"📊 ARIMA Return Prediction: {prediction_results['arima_return_prediction']:.4f} ({prediction_results['arima_return_prediction']*100:.2f}%)")
    
    print(f"\n🤖 Individual Model Predictions:")
    for model, pred in prediction_results['individual_predictions'].items():
        print(f"   {model}: {pred:.4f} ({pred*100:.2f}%)")

# Create comprehensive visualization
if prediction_results:
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Recent price trend
    recent_data = btc_data.tail(60)  # Last 60 days
    axes[0,0].plot(recent_data.index, recent_data['close'], linewidth=2, label='Actual Price')
    
    # Add prediction point
    next_date = recent_data.index[-1] + pd.Timedelta(days=1)
    predicted_price = prediction_results['current_price'] * (1 + prediction_results['ensemble_return_prediction'])
    axes[0,0].scatter([next_date], [predicted_price], color='red', s=100, label='Ensemble Prediction', zorder=5)
    
    arima_price = prediction_results['arima_price_prediction']
    axes[0,0].scatter([next_date], [arima_price], color='orange', s=100, label='ARIMA Prediction', zorder=5)
    
    axes[0,0].set_title('Recent Price Trend & Predictions')
    axes[0,0].set_ylabel('Price ($)')
    axes[0,0].legend()
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # Model predictions comparison
    models = list(prediction_results['individual_predictions'].keys())
    predictions = list(prediction_results['individual_predictions'].values())
    
    colors = ['skyblue', 'lightgreen', 'salmon']
    bars = axes[0,1].bar(models, predictions, color=colors)
    axes[0,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[0,1].set_title('Individual Model Predictions')
    axes[0,1].set_ylabel('Predicted Return')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # Add value labels on bars
    for bar, pred in zip(bars, predictions):
        height = bar.get_height()
        axes[0,1].text(bar.get_x() + bar.get_width()/2., height + (0.001 if height > 0 else -0.002),
                      f'{pred:.3f}', ha='center', va='bottom' if height > 0 else 'top')
    
    # Recent volatility analysis
    recent_returns = btc_data['close'].pct_change().tail(30)
    axes[1,0].plot(recent_returns.index, recent_returns, alpha=0.7, label='Daily Returns')
    axes[1,0].axhline(y=recent_returns.mean(), color='red', linestyle='--', label=f'Mean: {recent_returns.mean():.3f}')
    axes[1,0].axhline(y=recent_returns.std(), color='orange', linestyle='--', label=f'Std: {recent_returns.std():.3f}')
    axes[1,0].axhline(y=-recent_returns.std(), color='orange', linestyle='--')
    axes[1,0].set_title('Recent Volatility (30 days)')
    axes[1,0].set_ylabel('Daily Return')
    axes[1,0].legend()
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # Prediction confidence
    pred_values = list(prediction_results['individual_predictions'].values())
    pred_mean = np.mean(pred_values)
    pred_std = np.std(pred_values)
    
    axes[1,1].bar(['Ensemble', 'ARIMA'], 
                 [prediction_results['ensemble_return_prediction'], prediction_results['arima_return_prediction']],
                 color=['blue', 'orange'], alpha=0.7)
    axes[1,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1,1].set_title('Final Predictions Comparison')
    axes[1,1].set_ylabel('Predicted Return')
    
    plt.tight_layout()
    plt.show()
    
    # Summary statistics
    print("\n📊 Analysis Summary:")
    print("=" * 50)
    print(f"Current Price: ${prediction_results['current_price']:,.2f}")
    print(f"Ensemble Prediction: {prediction_results['ensemble_return_prediction']*100:+.2f}%")
    print(f"ARIMA Prediction: {prediction_results['arima_return_prediction']*100:+.2f}%")
    print(f"Recent 30-day volatility: {recent_returns.std()*100:.2f}%")
    
    # Risk assessment
    ensemble_return = prediction_results['ensemble_return_prediction']
    volatility = recent_returns.std()
    
    if abs(ensemble_return) > 2 * volatility:
        risk_level = "HIGH"
    elif abs(ensemble_return) > volatility:
        risk_level = "MEDIUM"
    else:
        risk_level = "LOW"
    
    print(f"\n⚠️ Risk Assessment: {risk_level}")
    print(f"   Prediction magnitude vs recent volatility: {abs(ensemble_return)/volatility:.1f}x")

else:
    print("❌ Cannot create visualizations - prediction system failed")

# Test the complete system
print("🧪 Running comprehensive system tests...")
print("=" * 50)

# Test 1: Data fetching
print("\n1. Testing data fetching...")
try:
    test_data = get_bitcoin_data(period="1y")
    assert len(test_data) > 300, "Not enough data points"
    assert 'close' in test_data.columns, "Missing close price column"
    print("   ✅ Data fetching works correctly")
except Exception as e:
    print(f"   ❌ Data fetching failed: {str(e)}")

# Test 2: Feature engineering
print("\n2. Testing feature engineering...")
try:
    test_features = create_features(test_data)
    assert len(test_features.columns) > 50, "Not enough features created"
    assert 'target_return_1d' in test_features.columns, "Missing target variable"
    print(f"   ✅ Feature engineering works correctly ({len(test_features.columns)} features)")
except Exception as e:
    print(f"   ❌ Feature engineering failed: {str(e)}")

# Test 3: Model loading
print("\n3. Testing model loading...")
models_loaded = 0
try:
    if 'rf_1d_model' in globals():
        models_loaded += 1
    if 'xgb_1d_model' in globals():
        models_loaded += 1
    if 'lgb_1d_model' in globals():
        models_loaded += 1
    if 'arima_price_model' in globals():
        models_loaded += 1
    
    print(f"   ✅ {models_loaded} models loaded successfully")
except Exception as e:
    print(f"   ❌ Model loading test failed: {str(e)}")

# Test 4: Prediction system
print("\n4. Testing prediction system...")
try:
    test_prediction = make_ensemble_prediction(test_data)
    if test_prediction:
        assert 'current_price' in test_prediction, "Missing current price"
        assert 'ensemble_return_prediction' in test_prediction, "Missing ensemble prediction"
        assert isinstance(test_prediction['current_price'], (int, float)), "Invalid price type"
        print("   ✅ Prediction system works correctly")
        print(f"      Sample prediction: {test_prediction['ensemble_return_prediction']*100:.2f}%")
    else:
        print("   ⚠️ Prediction system returned None")
except Exception as e:
    print(f"   ❌ Prediction system failed: {str(e)}")

# Test 5: Core project files (check if essential files exist)
print("\n5. Testing core project files...")
core_files = [
    'feature_engineering.py',
    'tree_models.py',
    'arima_model.py',
    'config.yaml'
]

files_exist = 0
for file_path in core_files:
    if Path(file_path).exists():
        files_exist += 1

print(f"   📁 {files_exist}/{len(core_files)} core files available")
if files_exist == len(core_files):
    print("   ✅ Core project files ready")
else:
    print("   ⚠️ Some core files missing")

print("\n" + "=" * 50)
print("🎉 System testing complete!")
print(f"📊 Overall health: {(models_loaded + files_exist + (2 if test_prediction else 0))/7*100:.0f}%")

# Final summary
print("🎯 Bitcoin Price Prediction System - Final Summary")
print("=" * 60)
print(f"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"💾 Data Points Analyzed: {len(btc_data):,}")
print(f"🔧 Features Created: {len(btc_features.columns)}")
print(f"🤖 Models Integrated: {models_loaded if 'models_loaded' in locals() else 'Unknown'}")

if prediction_results:
    print(f"\n📈 Latest Prediction:")
    print(f"   Current Price: ${prediction_results['current_price']:,.2f}")
    print(f"   Ensemble Forecast: {prediction_results['ensemble_return_prediction']*100:+.2f}%")
    print(f"   ARIMA Forecast: {prediction_results['arima_return_prediction']*100:+.2f}%")

print(f"\n🚀 Core System Status: {'Ready' if files_exist == len(core_files) else 'Partial'}")
print(f"📊 System Health: {(models_loaded + files_exist + (2 if prediction_results else 0))/7*100:.0f}%")

print("\n" + "=" * 60)
print("✨ Thank you for exploring Bitcoin price prediction with us!")
print("💡 Remember: Use predictions as signals, not absolute truths.")
print("🔬 Keep experimenting and improving the models!")