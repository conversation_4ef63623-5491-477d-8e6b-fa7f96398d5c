#!/usr/bin/env python3
"""
Production Model Manager for Bitcoin Price Prediction
====================================================

This module manages the loading and inference of pre-trained models
for production Bitcoin price prediction system.

Author: Bitcoin Price Prediction Production System
Date: 2025-07-07
"""

import pandas as pd
import numpy as np
import joblib
import json
import warnings
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionModelManager:
    """
    Production-ready model manager that loads and manages pre-trained models
    for Bitcoin price prediction across multiple timeframes.
    """
    
    def __init__(self, config_path: str = "../config.yaml"):
        """Initialize the production model manager."""
        import yaml
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.models = {}
        self.scaler = None
        self.model_results = {}
        self.feature_columns = None
        
        # Load all models and metadata
        self._load_models()
        self._load_results()
        
        logger.info("Production Model Manager initialized successfully")
    
    def _load_models(self):
        """Load all pre-trained models from disk."""
        model_paths = self.config['models']['model_paths']
        
        try:
            # Load ARIMA models
            self.models['arima_price'] = joblib.load(model_paths['arima_price'])
            self.models['arima_returns'] = joblib.load(model_paths['arima_returns'])
            logger.info("✓ ARIMA models loaded successfully")
            
            # Load tree-based models for different timeframes
            timeframes = ['1d', '7d', '30d']
            model_types = ['random_forest', 'xgboost', 'lightgbm']
            
            for timeframe in timeframes:
                for model_type in model_types:
                    key = f"{model_type}_{timeframe}"
                    model_path = model_paths[key]
                    self.models[key] = joblib.load(model_path)
                    logger.info(f"✓ {model_type.upper()} {timeframe} model loaded")
            
            # Load scaler
            self.scaler = joblib.load(model_paths['scaler'])
            logger.info("✓ Feature scaler loaded successfully")
            
            # Load feature columns from a sample prediction to ensure consistency
            self._load_feature_columns()
            
        except Exception as e:
            logger.error(f"Error loading models: {str(e)}")
            raise
    
    def _load_feature_columns(self):
        """Load feature column names from existing processed data."""
        try:
            # Load processed data to get feature columns
            data_path = self.config['data']['paths']['existing_processed']
            sample_data = pd.read_csv(data_path, nrows=1, index_col=0)
            
            # Extract feature columns (exclude target columns)
            target_cols = [col for col in sample_data.columns if col.startswith('target_')]
            self.feature_columns = [col for col in sample_data.columns if col not in target_cols]
            
            logger.info(f"✓ Feature columns loaded: {len(self.feature_columns)} features")
            
        except Exception as e:
            logger.error(f"Error loading feature columns: {str(e)}")
            raise
    
    def _load_results(self):
        """Load model performance results."""
        try:
            results_paths = self.config['models']['results_paths']
            
            # Load ARIMA results
            with open(results_paths['arima_results'], 'r') as f:
                self.model_results['arima'] = json.load(f)
            
            # Load tree model results
            with open(results_paths['tree_models_results'], 'r') as f:
                self.model_results['tree_models'] = json.load(f)
            
            logger.info("✓ Model performance results loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model results: {str(e)}")
            raise
    
    def get_model_performance(self, model_type: str, timeframe: str = None) -> Dict:
        """
        Get performance metrics for a specific model.
        
        Args:
            model_type: Type of model ('arima', 'random_forest', 'xgboost', 'lightgbm')
            timeframe: Timeframe for tree models ('1d', '7d', '30d')
        
        Returns:
            Dictionary containing performance metrics
        """
        if model_type == 'arima':
            return self.model_results['arima']
        else:
            target_key = f"target_return_{timeframe}"
            if target_key in self.model_results['tree_models']:
                model_key = model_type.replace('_', '').title()
                if model_key == 'Lightgbm':
                    model_key = 'LightGBM'
                return self.model_results['tree_models'][target_key].get(model_key, {})
        
        return {}
    
    def predict_arima(self, steps: int = 1) -> Dict[str, Any]:
        """
        Make ARIMA predictions for price and returns.
        
        Args:
            steps: Number of steps to forecast
        
        Returns:
            Dictionary containing price and return predictions
        """
        try:
            # Price predictions
            price_forecast = self.models['arima_price'].forecast(steps=steps)
            price_conf_int = self.models['arima_price'].get_forecast(steps=steps).conf_int()
            
            # Return predictions
            return_forecast = self.models['arima_returns'].forecast(steps=steps)
            return_conf_int = self.models['arima_returns'].get_forecast(steps=steps).conf_int()
            
            return {
                'price_forecast': price_forecast.tolist() if hasattr(price_forecast, 'tolist') else [price_forecast],
                'price_conf_int': price_conf_int.values.tolist() if hasattr(price_conf_int, 'values') else [],
                'return_forecast': return_forecast.tolist() if hasattr(return_forecast, 'tolist') else [return_forecast],
                'return_conf_int': return_conf_int.values.tolist() if hasattr(return_conf_int, 'values') else [],
                'forecast_dates': [(datetime.now() + timedelta(days=i+1)).strftime('%Y-%m-%d') for i in range(steps)]
            }
            
        except Exception as e:
            logger.error(f"Error in ARIMA prediction: {str(e)}")
            return {}
    
    def predict_tree_models(self, features: pd.DataFrame, timeframe: str = '1d') -> Dict[str, Any]:
        """
        Make predictions using tree-based models for a specific timeframe.
        
        Args:
            features: Feature DataFrame for prediction
            timeframe: Prediction timeframe ('1d', '7d', '30d')
        
        Returns:
            Dictionary containing predictions from all tree models
        """
        try:
            # Ensure features are in the correct order and scaled
            if self.feature_columns:
                features = features[self.feature_columns]
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            predictions = {}
            model_types = ['random_forest', 'xgboost', 'lightgbm']
            
            for model_type in model_types:
                model_key = f"{model_type}_{timeframe}"
                if model_key in self.models:
                    pred = self.models[model_key].predict(features_scaled)
                    predictions[model_type] = pred.tolist() if hasattr(pred, 'tolist') else [pred]
            
            return {
                'timeframe': timeframe,
                'predictions': predictions,
                'feature_count': len(self.feature_columns) if self.feature_columns else 0,
                'prediction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"Error in tree model prediction: {str(e)}")
            return {}
    
    def get_ensemble_prediction(self, features: pd.DataFrame, timeframe: str = '1d') -> Dict[str, Any]:
        """
        Get ensemble prediction combining all tree models for a timeframe.
        
        Args:
            features: Feature DataFrame for prediction
            timeframe: Prediction timeframe ('1d', '7d', '30d')
        
        Returns:
            Dictionary containing ensemble prediction and individual model predictions
        """
        try:
            tree_predictions = self.predict_tree_models(features, timeframe)
            
            if not tree_predictions or 'predictions' not in tree_predictions:
                return {}
            
            # Calculate ensemble prediction (simple average)
            individual_preds = tree_predictions['predictions']
            if individual_preds:
                # Convert to numpy arrays for easier calculation
                pred_arrays = [np.array(pred) for pred in individual_preds.values()]
                ensemble_pred = np.mean(pred_arrays, axis=0)
                
                # Calculate prediction confidence (inverse of standard deviation)
                pred_std = np.std(pred_arrays, axis=0)
                confidence = 1 / (1 + pred_std)  # Higher confidence for lower std
                
                return {
                    'timeframe': timeframe,
                    'ensemble_prediction': ensemble_pred.tolist(),
                    'prediction_confidence': confidence.tolist(),
                    'individual_predictions': individual_preds,
                    'model_count': len(individual_preds),
                    'prediction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            
            return tree_predictions
            
        except Exception as e:
            logger.error(f"Error in ensemble prediction: {str(e)}")
            return {}
    
    def get_model_summary(self) -> Dict[str, Any]:
        """Get summary of all loaded models and their performance."""
        summary = {
            'loaded_models': list(self.models.keys()),
            'model_count': len(self.models),
            'feature_count': len(self.feature_columns) if self.feature_columns else 0,
            'scaler_loaded': self.scaler is not None,
            'results_loaded': len(self.model_results),
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Add performance summary
        if 'tree_models' in self.model_results:
            summary['performance_summary'] = {}
            for timeframe in ['target_return_1d', 'target_return_7d', 'target_return_30d']:
                if timeframe in self.model_results['tree_models']:
                    timeframe_short = timeframe.split('_')[-1]
                    summary['performance_summary'][timeframe_short] = {}
                    
                    for model in ['RandomForest', 'XGBoost', 'LightGBM']:
                        if model in self.model_results['tree_models'][timeframe]:
                            test_metrics = self.model_results['tree_models'][timeframe][model].get('test_metrics', {})
                            summary['performance_summary'][timeframe_short][model.lower()] = {
                                'rmse': test_metrics.get('rmse', 'N/A'),
                                'r2': test_metrics.get('r2', 'N/A'),
                                'directional_accuracy': test_metrics.get('directional_accuracy', 'N/A')
                            }
        
        return summary
