"""
Machine Learning Models for Bitcoin Price Prediction

This module implements various ML models including:
- Random Forest
- XGBoost
- LightGBM
- Support Vector Regression
- Neural Networks
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
import joblib
import warnings
warnings.filterwarnings('ignore')


class MLPredictor:
    """
    Base class for machine learning predictors
    """
    
    def __init__(self, model, scaler=None):
        self.model = model
        self.scaler = scaler
        self.is_fitted = False
        self.feature_importance_ = None
        self.feature_names_ = None
    
    def prepare_features(self, data, target_col, feature_cols=None, lag_features=True):
        """
        Prepare features for ML models
        
        Args:
            data: DataFrame with time series data
            target_col: Target column name
            feature_cols: List of feature columns (if None, use all except target)
            lag_features: Whether to create lag features
            
        Returns:
            X (features), y (target)
        """
        df = data.copy()
        
        if feature_cols is None:
            feature_cols = [col for col in df.columns if col != target_col]
        
        # Create lag features if requested
        if lag_features:
            for lag in [1, 2, 3, 5, 7]:
                for col in [target_col]:
                    df[f'{col}_lag_{lag}'] = df[col].shift(lag)
                    feature_cols.append(f'{col}_lag_{lag}')
        
        # Remove rows with NaN values
        df = df.dropna()
        
        X = df[feature_cols]
        y = df[target_col]
        
        self.feature_names_ = feature_cols
        
        return X, y
    
    def fit(self, X, y):
        """
        Fit the model
        
        Args:
            X: Features
            y: Target
        """
        try:
            # Scale features if scaler is provided
            if self.scaler is not None:
                X_scaled = self.scaler.fit_transform(X)
            else:
                X_scaled = X
            
            # Fit the model
            self.model.fit(X_scaled, y)
            self.is_fitted = True
            
            # Extract feature importance if available
            if hasattr(self.model, 'feature_importances_'):
                self.feature_importance_ = dict(zip(self.feature_names_, self.model.feature_importances_))
            elif hasattr(self.model, 'coef_'):
                self.feature_importance_ = dict(zip(self.feature_names_, np.abs(self.model.coef_)))
            
            print(f"{self.model.__class__.__name__} fitted successfully")
            
        except Exception as e:
            print(f"Error fitting model: {e}")
            self.is_fitted = False
    
    def predict(self, X):
        """
        Make predictions
        
        Args:
            X: Features
            
        Returns:
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        try:
            # Scale features if scaler was used
            if self.scaler is not None:
                X_scaled = self.scaler.transform(X)
            else:
                X_scaled = X
            
            predictions = self.model.predict(X_scaled)
            return predictions
            
        except Exception as e:
            print(f"Error making predictions: {e}")
            return None
    
    def get_feature_importance(self, top_n=20):
        """
        Get top N most important features
        
        Args:
            top_n: Number of top features to return
            
        Returns:
            Dictionary of feature importance scores
        """
        if self.feature_importance_ is None:
            return None
        
        sorted_features = sorted(self.feature_importance_.items(), 
                               key=lambda x: x[1], reverse=True)
        
        return dict(sorted_features[:top_n])


class RandomForestPredictor(MLPredictor):
    """
    Random Forest predictor for Bitcoin price prediction
    """
    
    def __init__(self, n_estimators=100, max_depth=None, random_state=42):
        model = RandomForestRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            random_state=random_state,
            n_jobs=-1
        )
        super().__init__(model, scaler=None)


class XGBoostPredictor(MLPredictor):
    """
    XGBoost predictor for Bitcoin price prediction
    """
    
    def __init__(self, n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42):
        model = xgb.XGBRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            learning_rate=learning_rate,
            random_state=random_state,
            n_jobs=-1
        )
        super().__init__(model, scaler=None)


class LightGBMPredictor(MLPredictor):
    """
    LightGBM predictor for Bitcoin price prediction
    """
    
    def __init__(self, n_estimators=100, max_depth=-1, learning_rate=0.1, random_state=42):
        model = lgb.LGBMRegressor(
            n_estimators=n_estimators,
            max_depth=max_depth,
            learning_rate=learning_rate,
            random_state=random_state,
            n_jobs=-1,
            verbose=-1
        )
        super().__init__(model, scaler=None)


class SVRPredictor(MLPredictor):
    """
    Support Vector Regression predictor for Bitcoin price prediction
    """
    
    def __init__(self, kernel='rbf', C=1.0, gamma='scale'):
        model = SVR(kernel=kernel, C=C, gamma=gamma)
        scaler = StandardScaler()
        super().__init__(model, scaler)


class NeuralNetworkPredictor(MLPredictor):
    """
    Neural Network predictor for Bitcoin price prediction
    """
    
    def __init__(self, hidden_layer_sizes=(100, 50), activation='relu', 
                 learning_rate_init=0.001, max_iter=500, random_state=42):
        model = MLPRegressor(
            hidden_layer_sizes=hidden_layer_sizes,
            activation=activation,
            learning_rate_init=learning_rate_init,
            max_iter=max_iter,
            random_state=random_state
        )
        scaler = StandardScaler()
        super().__init__(model, scaler)


class MLEnsemble:
    """
    Ensemble of machine learning models
    """
    
    def __init__(self):
        self.models = {}
        self.weights = {}
        self.is_fitted = False
    
    def add_model(self, name, model, weight=1.0):
        """
        Add a model to the ensemble
        
        Args:
            name: Model name
            model: Model instance
            weight: Model weight in ensemble
        """
        self.models[name] = model
        self.weights[name] = weight
    
    def fit(self, X, y):
        """
        Fit all models in the ensemble
        
        Args:
            X: Features
            y: Target
        """
        print("Fitting ensemble models...")
        
        for name, model in self.models.items():
            print(f"Fitting {name}...")
            model.fit(X, y)
        
        self.is_fitted = True
        print("Ensemble fitting completed")
    
    def predict(self, X):
        """
        Make ensemble predictions
        
        Args:
            X: Features
            
        Returns:
            Weighted ensemble predictions
        """
        if not self.is_fitted:
            raise ValueError("Ensemble must be fitted before making predictions")
        
        predictions = {}
        total_weight = sum(self.weights.values())
        
        # Get predictions from each model
        for name, model in self.models.items():
            if model.is_fitted:
                pred = model.predict(X)
                if pred is not None:
                    predictions[name] = pred
        
        if not predictions:
            return None
        
        # Calculate weighted average
        ensemble_pred = np.zeros(len(list(predictions.values())[0]))
        for name, pred in predictions.items():
            weight = self.weights[name] / total_weight
            ensemble_pred += weight * pred
        
        return ensemble_pred, predictions
    
    def get_ensemble_feature_importance(self):
        """
        Get aggregated feature importance from all models
        
        Returns:
            Dictionary of aggregated feature importance
        """
        all_importance = {}
        total_weight = sum(self.weights.values())
        
        for name, model in self.models.items():
            if model.feature_importance_ is not None:
                weight = self.weights[name] / total_weight
                
                for feature, importance in model.feature_importance_.items():
                    if feature not in all_importance:
                        all_importance[feature] = 0
                    all_importance[feature] += weight * importance
        
        return all_importance


def calculate_directional_accuracy(actual, predicted):
    """
    Calculate directional accuracy (percentage of correct direction predictions)
    
    Args:
        actual: Actual values
        predicted: Predicted values
        
    Returns:
        Directional accuracy percentage
    """
    actual_direction = np.diff(actual) > 0
    predicted_direction = np.diff(predicted) > 0
    
    correct_directions = np.sum(actual_direction == predicted_direction)
    total_directions = len(actual_direction)
    
    return (correct_directions / total_directions) * 100


def evaluate_ml_models(data, target_col='Close', test_size=0.2, feature_cols=None):
    """
    Comprehensive evaluation of ML models
    
    Args:
        data: DataFrame with time series data
        target_col: Target column name
        test_size: Proportion of data for testing
        feature_cols: List of feature columns
        
    Returns:
        Dictionary with model results
    """
    # Split data chronologically
    split_idx = int(len(data) * (1 - test_size))
    train_data = data[:split_idx]
    test_data = data[split_idx:]
    
    results = {}
    
    # Initialize models
    models = {
        'Random_Forest': RandomForestPredictor(n_estimators=100),
        'XGBoost': XGBoostPredictor(n_estimators=100),
        'LightGBM': LightGBMPredictor(n_estimators=100),
        'SVR': SVRPredictor(),
        'Neural_Network': NeuralNetworkPredictor()
    }
    
    # Prepare features
    base_model = MLPredictor(None)
    X_train, y_train = base_model.prepare_features(train_data, target_col, feature_cols)
    X_test, y_test = base_model.prepare_features(test_data, target_col, feature_cols)
    
    # Align test data
    common_index = X_test.index.intersection(y_test.index)
    X_test = X_test.loc[common_index]
    y_test = y_test.loc[common_index]
    
    # Evaluate each model
    for name, model in models.items():
        print(f"Evaluating {name}...")
        
        try:
            # Fit model
            model.fit(X_train, y_train)
            
            if model.is_fitted:
                # Make predictions
                predictions = model.predict(X_test)
                
                if predictions is not None:
                    # Calculate metrics
                    mae = mean_absolute_error(y_test, predictions)
                    rmse = np.sqrt(mean_squared_error(y_test, predictions))
                    mape = np.mean(np.abs((y_test - predictions) / y_test)) * 100
                    r2 = r2_score(y_test, predictions)
                    directional_acc = calculate_directional_accuracy(y_test.values, predictions)
                    
                    results[name] = {
                        'predictions': predictions,
                        'MAE': mae,
                        'RMSE': rmse,
                        'MAPE': mape,
                        'R2': r2,
                        'Directional_Accuracy': directional_acc,
                        'feature_importance': model.get_feature_importance()
                    }
                    
                    print(f"{name} - MAPE: {mape:.2f}%, Directional Accuracy: {directional_acc:.2f}%")
        
        except Exception as e:
            print(f"Error evaluating {name}: {e}")
            continue
    
    return results, y_test


def hyperparameter_tuning(model_class, X_train, y_train, param_grid, cv_folds=3):
    """
    Perform hyperparameter tuning using time series cross-validation
    
    Args:
        model_class: Model class to tune
        X_train: Training features
        y_train: Training target
        param_grid: Parameter grid for tuning
        cv_folds: Number of CV folds
        
    Returns:
        Best parameters and best score
    """
    # Create time series cross-validator
    tscv = TimeSeriesSplit(n_splits=cv_folds)
    
    # Create base model
    base_model = model_class()
    
    # Perform grid search
    grid_search = GridSearchCV(
        base_model.model,
        param_grid,
        cv=tscv,
        scoring='neg_mean_squared_error',
        n_jobs=-1,
        verbose=1
    )
    
    # Fit grid search
    if base_model.scaler is not None:
        X_scaled = base_model.scaler.fit_transform(X_train)
    else:
        X_scaled = X_train
    
    grid_search.fit(X_scaled, y_train)
    
    return grid_search.best_params_, grid_search.best_score_


if __name__ == "__main__":
    # Example usage
    import yfinance as yf
    from data_collection import BitcoinDataCollector
    
    # Collect data
    collector = BitcoinDataCollector()
    data = collector.get_market_data(period="1y", include_indicators=True)
    
    if data is not None:
        # Evaluate models
        results, y_test = evaluate_ml_models(data)
        
        # Print results summary
        print("\n" + "="*50)
        print("MODEL EVALUATION SUMMARY")
        print("="*50)
        
        for model_name, metrics in results.items():
            print(f"\n{model_name}:")
            print(f"  MAPE: {metrics['MAPE']:.2f}%")
            print(f"  RMSE: {metrics['RMSE']:.2f}")
            print(f"  R²: {metrics['R2']:.4f}")
            print(f"  Directional Accuracy: {metrics['Directional_Accuracy']:.2f}%")
    
    else:
        print("Failed to collect data for evaluation")
